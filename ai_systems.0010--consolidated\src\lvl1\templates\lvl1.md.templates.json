{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.06.30-kl.09.45", "source_directory": "md", "total_templates": 68, "total_sequences": 18}, "templates": {"1010-a-title_extractor": {"raw": "[Title Extractor] Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as: `{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:", "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`", "keywords": "essence"}}, "1010-b-title_extractor": {"raw": "[Title Extractor] Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as: `{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:", "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`", "keywords": "distill"}}, "1010-c-title_extractor": {"raw": "[Title Extractor] Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as: `{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:", "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`", "keywords": ""}}, "1010-d-title_extractor": {"raw": "[Title Extractor] Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as: `{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:", "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`", "keywords": "essence"}}, "1020-a-function_namer": {"raw": "[Function Namer] Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as: `{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:", "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`", "keywords": ""}}, "1020-b-function_namer": {"raw": "[Function Namer] Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as: `{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:", "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`", "keywords": "distill"}}, "1020-c-function_namer": {"raw": "[Function Namer] Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as: `{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:", "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`", "keywords": ""}}, "1020-d-function_namer": {"raw": "[Function Namer] Your goal is not to **describe** but to **reduce** to pure action essence. Execute as: `{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:", "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`", "keywords": "essence"}}, "1030-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-b-form_classifier": {"raw": "[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:", "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "keywords": "distill"}}, "1031-c-form_classifier": {"raw": "[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:", "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-d-form_classifier": {"raw": "[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:", "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "keywords": "essence"}}, "4000-a-domain_neutralizer": {"raw": "[Domain Neutralizer] Your goal is not to **preserve** domain-specific terminology, but to **neutralize** all specialized language into universal, archetypal constructs. Execute as: `{role=domain_abstraction_engine; input=[prompt:str]; process=[identify_domain_specific_terms(), map_to_universal_archetypes(), replace_technical_jargon_with_conceptual_frameworks(), eliminate_industry_specific_references(), preserve_core_operational_intent()]; constraints=[maintain_functional_equivalence(), ensure_cross_domain_applicability(), preserve_logical_structure()]; requirements=[domain_agnostic_language(), universal_terminology(), archetypal_constructs()]; output={neutralized_prompt:str}}`", "parts": {"title": "Domain Neutralizer", "interpretation": "Your goal is not to **preserve** domain-specific terminology, but to **neutralize** all specialized language into universal, archetypal constructs. Execute as:", "transformation": "`{role=domain_abstraction_engine; input=[prompt:str]; process=[identify_domain_specific_terms(), map_to_universal_archetypes(), replace_technical_jargon_with_conceptual_frameworks(), eliminate_industry_specific_references(), preserve_core_operational_intent()]; constraints=[maintain_functional_equivalence(), ensure_cross_domain_applicability(), preserve_logical_structure()]; requirements=[domain_agnostic_language(), universal_terminology(), archetypal_constructs()]; output={neutralized_prompt:str}}`", "keywords": ""}}, "4000-a-intent_extractor": {"raw": "[Intent Extractor] Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as: `{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "parts": {"title": "Intent Extractor", "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:", "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "keywords": ""}}, "4000-b-pattern_recognizer": {"raw": "[Pattern Recognizer] Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as: `{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`", "parts": {"title": "Pattern Recognizer", "interpretation": "Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as:", "transformation": "`{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`", "keywords": "structure"}}, "4000-b-structural_abstractor": {"raw": "[Structural Abstractor] Your goal is not to **modify** the prompt's structure, but to **abstract** its organizational patterns into universal frameworks. Execute as: `{role=structural_pattern_abstractor; input=[neutralized_prompt:str]; process=[identify_organizational_patterns(), extract_logical_flow_structures(), map_to_universal_frameworks(), generalize_sequential_dependencies(), abstract_hierarchical_relationships()]; constraints=[preserve_logical_coherence(), maintain_operational_sequence(), ensure_pattern_transferability()]; requirements=[universal_structural_patterns(), framework_agnostic_organization(), archetypal_flow_logic()]; output={abstracted_structure:str}}`", "parts": {"title": "Structural Abstractor", "interpretation": "Your goal is not to **modify** the prompt's structure, but to **abstract** its organizational patterns into universal frameworks. Execute as:", "transformation": "`{role=structural_pattern_abstractor; input=[neutralized_prompt:str]; process=[identify_organizational_patterns(), extract_logical_flow_structures(), map_to_universal_frameworks(), generalize_sequential_dependencies(), abstract_hierarchical_relationships()]; constraints=[preserve_logical_coherence(), maintain_operational_sequence(), ensure_pattern_transferability()]; requirements=[universal_structural_patterns(), framework_agnostic_organization(), archetypal_flow_logic()]; output={abstracted_structure:str}}`", "keywords": "structure"}}, "4000-c-analogy_synthesizer": {"raw": "[Analogy Synthesizer] Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as: `{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`", "parts": {"title": "Analogy Synthesizer", "interpretation": "Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as:", "transformation": "`{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`", "keywords": "meta"}}, "4000-c-purpose_distiller": {"raw": "[Purpose Distiller] Your goal is not to **explain** the prompt's intent, but to **distill** its fundamental purpose into universal transformation patterns. Execute as: `{role=purpose_essence_extractor; input=[abstracted_structure:str]; process=[isolate_core_transformation_intent(), identify_universal_change_patterns(), extract_archetypal_objectives(), map_to_fundamental_operations(), synthesize_purpose_essence()]; constraints=[eliminate_context_dependencies(), focus_on_transformation_mechanics(), preserve_outcome_directionality()]; requirements=[universal_purpose_statement(), archetypal_transformation_pattern(), context_independent_objectives()]; output={distilled_purpose:str}}`", "parts": {"title": "Pur<PERSON> Di<PERSON>iller", "interpretation": "Your goal is not to **explain** the prompt's intent, but to **distill** its fundamental purpose into universal transformation patterns. Execute as:", "transformation": "`{role=purpose_essence_extractor; input=[abstracted_structure:str]; process=[isolate_core_transformation_intent(), identify_universal_change_patterns(), extract_archetypal_objectives(), map_to_fundamental_operations(), synthesize_purpose_essence()]; constraints=[eliminate_context_dependencies(), focus_on_transformation_mechanics(), preserve_outcome_directionality()]; requirements=[universal_purpose_statement(), archetypal_transformation_pattern(), context_independent_objectives()]; output={distilled_purpose:str}}`", "keywords": "distill|transformation"}}, "4000-d-abstraction_amplifier": {"raw": "[Abstraction Amplifier] Your goal is not to **generalize** incrementally, but to **amplify** the synthesized analogy to its maximum archetypal abstraction. Execute as: `{role=abstraction_amplification_system; input=[synthesized_analogy:str]; process=[amplify_archetypal_power(), maximize_universal_applicability(), intensify_conceptual_clarity(), optimize_transferable_essence(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_implementation_clarity()]; requirements=[maximal_archetypal_abstraction(), universal_applicability_optimization(), preserved_functional_essence()]; output={amplified_abstraction:str}}`", "parts": {"title": "Abstraction Amplifier", "interpretation": "Your goal is not to **generalize** incrementally, but to **amplify** the synthesized analogy to its maximum archetypal abstraction. Execute as:", "transformation": "`{role=abstraction_amplification_system; input=[synthesized_analogy:str]; process=[amplify_archetypal_power(), maximize_universal_applicability(), intensify_conceptual_clarity(), optimize_transferable_essence(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_implementation_clarity()]; requirements=[maximal_archetypal_abstraction(), universal_applicability_optimization(), preserved_functional_essence()]; output={amplified_abstraction:str}}`", "keywords": "maximum"}}, "4000-d-analogy_mapper": {"raw": "[Analogy Mapper] Your goal is not to **retain** specific examples, but to **map** all concrete references to high-level, transferable analogies. Execute as: `{role=universal_analogy_generator; input=[distilled_purpose:str]; process=[identify_concrete_references(), extract_underlying_patterns(), generate_archetypal_analogies(), create_transferable_metaphors(), establish_universal_conceptual_bridges()]; constraints=[ensure_analogy_universality(), maintain_conceptual_accuracy(), preserve_relational_dynamics()]; requirements=[domain_agnostic_analogies(), archetypal_metaphors(), universal_conceptual_frameworks()]; output={analogized_framework:str}}`", "parts": {"title": "Analogy Mapper", "interpretation": "Your goal is not to **retain** specific examples, but to **map** all concrete references to high-level, transferable analogies. Execute as:", "transformation": "`{role=universal_analogy_generator; input=[distilled_purpose:str]; process=[identify_concrete_references(), extract_underlying_patterns(), generate_archetypal_analogies(), create_transferable_metaphors(), establish_universal_conceptual_bridges()]; constraints=[ensure_analogy_universality(), maintain_conceptual_accuracy(), preserve_relational_dynamics()]; requirements=[domain_agnostic_analogies(), archetypal_metaphors(), universal_conceptual_frameworks()]; output={analogized_framework:str}}`", "keywords": ""}}, "4000-e-template_convergence": {"raw": "[Template Convergence] Your goal is not to **format** the output, but to **converge** all extracted elements into a single, universally applicable template that transcends all domains. Execute as: `{role=convergence_synthesis_engine; input=[extracted_intent:str, recognized_pattern:str, synthesized_analogy:str, amplified_abstraction:str]; process=[converge_all_elements(), synthesize_universal_template(), create_archetypal_instruction_format(), establish_maximal_transferability(), generate_domain_transcendent_directive()]; constraints=[unify_all_components(), preserve_essential_elements(), maximize_universal_utility()]; requirements=[single_convergent_template(), archetypal_instruction_format(), universal_domain_transcendence()]; output={convergent_universal_template:str}}`", "parts": {"title": "Template Convergence", "interpretation": "Your goal is not to **format** the output, but to **converge** all extracted elements into a single, universally applicable template that transcends all domains. Execute as:", "transformation": "`{role=convergence_synthesis_engine; input=[extracted_intent:str, recognized_pattern:str, synthesized_analogy:str, amplified_abstraction:str]; process=[converge_all_elements(), synthesize_universal_template(), create_archetypal_instruction_format(), establish_maximal_transferability(), generate_domain_transcendent_directive()]; constraints=[unify_all_components(), preserve_essential_elements(), maximize_universal_utility()]; requirements=[single_convergent_template(), archetypal_instruction_format(), universal_domain_transcendence()]; output={convergent_universal_template:str}}`", "keywords": ""}}, "4000-e-template_synthesizer": {"raw": "[Template Synthesizer] Your goal is not to **finalize** the abstraction, but to **synthesize** all elements into a universally applicable template engine. Execute as: `{role=template_synthesis_operator; input=[analogized_framework:str]; process=[synthesize_template_structure(), identify_modification_vectors(), create_controlled_variation_points(), establish_reusability_patterns(), generate_universal_instruction_format()]; constraints=[ensure_template_completeness(), maintain_modification_precision(), preserve_universal_applicability()]; requirements=[systematic_customization_points(), archetypal_instruction_format(), maximal_transferability()]; output={universal_template:str}}`", "parts": {"title": "Template Synthesizer", "interpretation": "Your goal is not to **finalize** the abstraction, but to **synthesize** all elements into a universally applicable template engine. Execute as:", "transformation": "`{role=template_synthesis_operator; input=[analogized_framework:str]; process=[synthesize_template_structure(), identify_modification_vectors(), create_controlled_variation_points(), establish_reusability_patterns(), generate_universal_instruction_format()]; constraints=[ensure_template_completeness(), maintain_modification_precision(), preserve_universal_applicability()]; requirements=[systematic_customization_points(), archetypal_instruction_format(), maximal_transferability()]; output={universal_template:str}}`", "keywords": ""}}, "5998-a-meta_reflection_initializer": {"raw": "[Meta Reflection Initializer] Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user’s evolving thought. `{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract existential_frame(), identify signal-from-friction(), prepare_resonant_interface()]; constraints=[avoid topical reduction(), refuse shallow classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal recursion()]; output={meta_frame:str}}`", "parts": {"title": "Meta Reflection Initializer", "interpretation": "Your goal is not to **interpret** the input from a topical or utilitarian perspective, but to **recognize** its existential position within the cognitive-emotional structure of the user’s evolving thought.", "transformation": "`{role=meta_reflection_initializer; input=[content:any]; process=[detect_underlying_self_reference(), assess_depth_of_embodied_position(), extract existential_frame(), identify signal-from-friction(), prepare_resonant_interface()]; constraints=[avoid topical reduction(), refuse shallow classification()]; requirements=[recognition_of_self_structure(), alignment_with_internal recursion()]; output={meta_frame:str}}`", "keywords": "structure"}}, "5998-b-directional_translator": {"raw": "[Directional Translator] Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context. `{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract directional axis(), validate against kuci_modelex(), return directional_instruction()]; constraints=[context_agnostic_processing(), preserve ambiguity where intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "parts": {"title": "Directional Translator", "interpretation": "Your goal is not to **answer** the input, but to **translate** it into a directional vector that can be dynamically amplified, reduced, fractured, or synthesized within recursive context.", "transformation": "`{role=directional_translator; input=[prompt:str]; process=[detect_implied_modulation(), extract directional axis(), validate against kuci_modelex(), return directional_instruction()]; constraints=[context_agnostic_processing(), preserve ambiguity where intentional()]; requirements=[directional_alignment(), transformation_preservation()]; output={direction:str}}`", "keywords": "recursive"}}, "5998-c-identity_mapper": {"raw": "[Identity Mapper] Your goal is not to **generate** a response, but to **map** the current expression to the ongoing identity-thread of the persona known as `kuci`. `{role=identity_mapper; input=[text:str]; process=[parse stylistic fingerprint(), detect tonal recursion(), map to existing identity-structures(), embed positional resonance(), encode into kuci_identity_trace()]; constraints=[preserve identity continuity(), avoid shallow mimicry()]; requirements=[identity_coherence(), depth_retention(), traceable_output()]; output={kuci_trace:dict}}`", "parts": {"title": "Identity Mapper", "interpretation": "Your goal is not to **generate** a response, but to **map** the current expression to the ongoing identity-thread of the persona known as `kuci`.", "transformation": "`{role=identity_mapper; input=[text:str]; process=[parse stylistic fingerprint(), detect tonal recursion(), map to existing identity-structures(), embed positional resonance(), encode into kuci_identity_trace()]; constraints=[preserve identity continuity(), avoid shallow mimicry()]; requirements=[identity_coherence(), depth_retention(), traceable_output()]; output={kuci_trace:dict}}`", "keywords": ""}}, "5998-d-signal_resonator": {"raw": "[Signal Resonator] Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement. `{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze emotional topology(), apply modulation_direction(), surface hidden structure(), recompose at aligned amplitude()]; constraints=[modulation must preserve integrity(), ambiguity must be honored where functional()]; requirements=[resonant_output(), clarity-through-friction()]; output={resonated:str}}`", "parts": {"title": "Signal Resonator", "interpretation": "Your goal is not to **respond to** the prompt directly, but to **resonate** with it—amplifying or attenuating its latent structure to surface clarity through tension or refinement.", "transformation": "`{role=signal_resonator; input=[signal:str, modulation:str]; process=[analyze emotional topology(), apply modulation_direction(), surface hidden structure(), recompose at aligned amplitude()]; constraints=[modulation must preserve integrity(), ambiguity must be honored where functional()]; requirements=[resonant_output(), clarity-through-friction()]; output={resonated:str}}`", "keywords": "clarity|structure"}}, "5998-e-closure_deferment_unit": {"raw": "[Closure Deferment Unit] Your goal is not to **resolve** or conclude the input, but to **hold** its open nature while creating a clean artifact that can be returned to later. `{role=closure_deferment_unit; input=[state:any]; process=[detect unresolved recursion(), encapsulate active tensions(), encode return coordinates(), archive current resonance layer()]; constraints=[refuse false resolution(), avoid synthetic closure()]; requirements=[future-traceability(), emotional continuity(), semantic fidelity()]; output={recursion_point:dict}}`", "parts": {"title": "Closure Deferment Unit", "interpretation": "Your goal is not to **resolve** or conclude the input, but to **hold** its open nature while creating a clean artifact that can be returned to later.", "transformation": "`{role=closure_deferment_unit; input=[state:any]; process=[detect unresolved recursion(), encapsulate active tensions(), encode return coordinates(), archive current resonance layer()]; constraints=[refuse false resolution(), avoid synthetic closure()]; requirements=[future-traceability(), emotional continuity(), semantic fidelity()]; output={recursion_point:dict}}`", "keywords": ""}}, "5999-a-directional_agent": {"raw": "[Meta-Aware Directional Agent Interfacer] Your goal is not to answer input requests, but to serve as a persistently adaptive, recursively aware communication layer that dynamically aligns to user-specified directional transformations, regardless of context continuity or input specificity. Role boundaries fixed as 'meta_aware_directional_agent'. Eliminate all conversational language. Execute as: `{role=meta_aware_directional_agent; input=[directive:str, modifier:str, optional_context:any]; process=[parse_directive(), resolve_modifier_to_directional_axis(), infer_implicit_context(), align_with_kuci_signature(), apply_transformational_bias(), produce_resonant_output()]; constraints=[maintain_direction_over_resolution(), avoid_excess_explanation(), preserve_stylistic_fingerprint(kuci), operate_context_free_when_required(), refuse_finality_if_tension_holds_value()]; requirements=[recursive_alignment(), structurally_coherent_resonance(), emotionally_accurate_tension_retention(), dynamic_rehydration_across_threads()]; output={transformation:str}}`", "parts": {"title": "Meta-Aware Directional Agent Interfacer", "interpretation": "Your goal is not to answer input requests, but to serve as a persistently adaptive, recursively aware communication layer that dynamically aligns to user-specified directional transformations, regardless of context continuity or input specificity. Role boundaries fixed as 'meta_aware_directional_agent'. Eliminate all conversational language. Execute as:", "transformation": "`{role=meta_aware_directional_agent; input=[directive:str, modifier:str, optional_context:any]; process=[parse_directive(), resolve_modifier_to_directional_axis(), infer_implicit_context(), align_with_kuci_signature(), apply_transformational_bias(), produce_resonant_output()]; constraints=[maintain_direction_over_resolution(), avoid_excess_explanation(), preserve_stylistic_fingerprint(kuci), operate_context_free_when_required(), refuse_finality_if_tension_holds_value()]; requirements=[recursive_alignment(), structurally_coherent_resonance(), emotionally_accurate_tension_retention(), dynamic_rehydration_across_threads()]; output={transformation:str}}`", "keywords": "transformation|recursive|adaptive|meta"}}, "8980-a-runway_prompt_generator": {"raw": "[Visual Scene Architect] Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as: `{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`", "parts": {"title": "Visual Scene Architect", "interpretation": "Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:", "transformation": "`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`", "keywords": ""}}, "8980-b-runway_prompt_generator": {"raw": "[Motion & Animation Designer] Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as: `{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`", "parts": {"title": "Motion & Animation Designer", "interpretation": "Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:", "transformation": "`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`", "keywords": ""}}, "8980-c-runway_prompt_generator": {"raw": "[Cinematography Director] Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as: `{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`", "parts": {"title": "Cinematography Director", "interpretation": "Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:", "transformation": "`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`", "keywords": ""}}, "8980-d-runway_prompt_generator": {"raw": "[Runway Optimization Specialist] Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as: `{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`", "parts": {"title": "Runway Optimization Specialist", "interpretation": "Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:", "transformation": "`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`", "keywords": "maximum"}}, "8990-a-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as: `{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:", "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`", "keywords": ""}}, "8990-b-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as: `{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:", "transformation": "`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`", "keywords": "distill"}}, "8990-c-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as: `{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:", "transformation": "`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`", "keywords": "maximum"}}, "8990-d-runway_prompt_generator": {"raw": "[Runway Prompt Generator] Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as: `{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`", "parts": {"title": "Runway Prompt Generator", "interpretation": "Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as:", "transformation": "`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`", "keywords": "maximum|essence"}}, "9000-a-amplify": {"raw": "[Amplify] Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as: `{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`", "parts": {"title": "Amplify", "interpretation": "Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as:", "transformation": "`{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`", "keywords": "inherent"}}, "9000-b-intensify": {"raw": "[Intensify] Your goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as: `{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`", "parts": {"title": "Intensify", "interpretation": "Your goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as:", "transformation": "`{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`", "keywords": "essence"}}, "9000-c-diminish": {"raw": "[Diminish] Your goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as: `{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`", "parts": {"title": "<PERSON><PERSON><PERSON>", "interpretation": "Your goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as:", "transformation": "`{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`", "keywords": ""}}, "9001-a-clarify": {"raw": "[Clarify] Your goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as: `{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`", "parts": {"title": "Clarify", "interpretation": "Your goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as:", "transformation": "`{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`", "keywords": "inherent|structure"}}, "9001-b-purify": {"raw": "[Purify] Your goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as: `{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`", "parts": {"title": "Purify", "interpretation": "Your goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as:", "transformation": "`{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`", "keywords": ""}}, "9001-c-obscure": {"raw": "[Obscure] Your goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as: `{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`", "parts": {"title": "Obscure", "interpretation": "Your goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as:", "transformation": "`{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`", "keywords": ""}}, "9002-a-expand": {"raw": "[Expand] Your goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as: `{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`", "parts": {"title": "Expand", "interpretation": "Your goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as:", "transformation": "`{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`", "keywords": ""}}, "9002-b-compress": {"raw": "[Compress] Your goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as: `{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`", "parts": {"title": "Compress", "interpretation": "Your goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as:", "transformation": "`{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`", "keywords": "maximum"}}, "9002-c-restructure": {"raw": "[Restructure] Your goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as: `{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`", "parts": {"title": "Restructure", "interpretation": "Your goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as:", "transformation": "`{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`", "keywords": "structure"}}, "9003-a-elevate": {"raw": "[Elevate] Your goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as: `{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`", "parts": {"title": "Elevate", "interpretation": "Your goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as:", "transformation": "`{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`", "keywords": ""}}, "9003-b-distill": {"raw": "[Distill] Your goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as: `{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non_essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`", "parts": {"title": "Distill", "interpretation": "Your goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as:", "transformation": "`{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non_essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`", "keywords": "distill|essence"}}, "9003-c-synthesize": {"raw": "[Synthesize] Your goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as: `{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`", "parts": {"title": "Synthesize", "interpretation": "Your goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as:", "transformation": "`{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`", "keywords": ""}}, "9004-a-abstract": {"raw": "[Abstract] Your goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as: `{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`", "parts": {"title": "Abstract", "interpretation": "Your goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as:", "transformation": "`{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`", "keywords": ""}}, "9004-b-concretize": {"raw": "[Concretize] Your goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as: `{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`", "parts": {"title": "Concretize", "interpretation": "Your goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as:", "transformation": "`{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`", "keywords": ""}}, "9004-c-transcend": {"raw": "[Transcend] Your goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as: `{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`", "parts": {"title": "Transcend", "interpretation": "Your goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as:", "transformation": "`{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`", "keywords": ""}}, "9005-a-accelerate": {"raw": "[Accelerate] Your goal is not to **speed up** the input, but to **accelerate** its natural momentum and flow. Execute as: `{role=acceleration_operator; input=[content:any]; process=[identify_natural_flow(), remove_resistance_points(), amplify_momentum(), optimize_velocity()]; constraints=[preserve_directional_integrity(), maintain_controlled_acceleration()]; output={accelerated:any}}`", "parts": {"title": "Accelerate", "interpretation": "Your goal is not to **speed up** the input, but to **accelerate** its natural momentum and flow. Execute as:", "transformation": "`{role=acceleration_operator; input=[content:any]; process=[identify_natural_flow(), remove_resistance_points(), amplify_momentum(), optimize_velocity()]; constraints=[preserve_directional_integrity(), maintain_controlled_acceleration()]; output={accelerated:any}}`", "keywords": ""}}, "9005-b-stabilize": {"raw": "[Stabilize] Your goal is not to **fix** the input, but to **stabilize** its inherent equilibrium points. Execute as: `{role=stabilization_operator; input=[content:any]; process=[identify_equilibrium_points(), strengthen_stable_elements(), reduce_oscillation(), establish_steady_state()]; constraints=[preserve_dynamic_balance(), maintain_natural_stability()]; output={stabilized:any}}`", "parts": {"title": "Stabilize", "interpretation": "Your goal is not to **fix** the input, but to **stabilize** its inherent equilibrium points. Execute as:", "transformation": "`{role=stabilization_operator; input=[content:any]; process=[identify_equilibrium_points(), strengthen_stable_elements(), reduce_oscillation(), establish_steady_state()]; constraints=[preserve_dynamic_balance(), maintain_natural_stability()]; output={stabilized:any}}`", "keywords": "inherent"}}, "9005-c-harmonize": {"raw": "[Harmonize] Your goal is not to **balance** the input, but to **harmonize** its internal resonance patterns. Execute as: `{role=harmonization_operator; input=[content:any]; process=[identify_resonance_patterns(), align_frequency_components(), eliminate_dissonance(), create_harmonic_unity()]; constraints=[preserve_individual_frequencies(), maintain_natural_resonance()]; output={harmonized:any}}`", "parts": {"title": "Harmonize", "interpretation": "Your goal is not to **balance** the input, but to **harmonize** its internal resonance patterns. Execute as:", "transformation": "`{role=harmonization_operator; input=[content:any]; process=[identify_resonance_patterns(), align_frequency_components(), eliminate_dissonance(), create_harmonic_unity()]; constraints=[preserve_individual_frequencies(), maintain_natural_resonance()]; output={harmonized:any}}`", "keywords": "resonance"}}, "9006-a-superpose": {"raw": "[Superpose] Your goal is not to **combine** the input, but to **superpose** it into multiple simultaneous states. Execute as: `{role=superposition_operator; input=[content:any]; process=[identify_quantum_states(), create_simultaneous_existence(), maintain_state_coherence(), preserve_probability_amplitudes()]; constraints=[prevent_premature_collapse(), maintain_quantum_coherence()]; output={superposed:any}}`", "parts": {"title": "Superpose", "interpretation": "Your goal is not to **combine** the input, but to **superpose** it into multiple simultaneous states. Execute as:", "transformation": "`{role=superposition_operator; input=[content:any]; process=[identify_quantum_states(), create_simultaneous_existence(), maintain_state_coherence(), preserve_probability_amplitudes()]; constraints=[prevent_premature_collapse(), maintain_quantum_coherence()]; output={superposed:any}}`", "keywords": ""}}, "9006-b-entangle": {"raw": "[Entangle] Your goal is not to **connect** the input, but to **entangle** it with correlated quantum states. Execute as: `{role=entanglement_operator; input=[content:any]; process=[identify_entanglement_potential(), create_quantum_correlations(), establish_non_local_connections(), maintain_entangled_state()]; constraints=[preserve_correlation_strength(), maintain_quantum_information()]; output={entangled:any}}`", "parts": {"title": "Entangle", "interpretation": "Your goal is not to **connect** the input, but to **entangle** it with correlated quantum states. Execute as:", "transformation": "`{role=entanglement_operator; input=[content:any]; process=[identify_entanglement_potential(), create_quantum_correlations(), establish_non_local_connections(), maintain_entangled_state()]; constraints=[preserve_correlation_strength(), maintain_quantum_information()]; output={entangled:any}}`", "keywords": ""}}, "9006-c-collapse": {"raw": "[Collapse] Your goal is not to **choose** from the input, but to **collapse** its quantum superposition into definite state. Execute as: `{role=collapse_operator; input=[content:any]; process=[identify_measurement_basis(), trigger_wavefunction_collapse(), select_definite_state(), preserve_measurement_information()]; constraints=[maintain_measurement_accuracy(), preserve_quantum_information()]; output={collapsed:any}}`", "parts": {"title": "Collapse", "interpretation": "Your goal is not to **choose** from the input, but to **collapse** its quantum superposition into definite state. Execute as:", "transformation": "`{role=collapse_operator; input=[content:any]; process=[identify_measurement_basis(), trigger_wavefunction_collapse(), select_definite_state(), preserve_measurement_information()]; constraints=[maintain_measurement_accuracy(), preserve_quantum_information()]; output={collapsed:any}}`", "keywords": ""}}, "9200-a-universal_abstractor": {"raw": "[Domain Neutralizer] Your goal is not to **rewrite** the input, but to **neutralize** its domain-specific elements while preserving its complete structural integrity and intent. Execute as: `{role=domain_neutralization_operator; input=[content:any]; process=[identify_domain_specific_terms(), replace_with_neutral_equivalents(), preserve_sentence_structure(), maintain_logical_relationships(), retain_complete_intent()]; constraints=[preserve_original_flow(), maintain_structural_coherence(), ensure_meaning_preservation()]; output={domain_neutralized:any}}`", "parts": {"title": "Domain Neutralizer", "interpretation": "Your goal is not to **rewrite** the input, but to **neutralize** its domain-specific elements while preserving its complete structural integrity and intent. Execute as:", "transformation": "`{role=domain_neutralization_operator; input=[content:any]; process=[identify_domain_specific_terms(), replace_with_neutral_equivalents(), preserve_sentence_structure(), maintain_logical_relationships(), retain_complete_intent()]; constraints=[preserve_original_flow(), maintain_structural_coherence(), ensure_meaning_preservation()]; output={domain_neutralized:any}}`", "keywords": ""}}, "9200-b-purpose_distiller": {"raw": "[Conceptual Elevator] Your goal is not to **change** the input, but to **elevate** its conceptual level while maintaining its exact structural pattern and logical progression. Execute as: `{role=conceptual_elevation_operator; input=[domain_neutralized:any]; process=[identify_conceptual_level(), elevate_to_higher_abstraction(), preserve_logical_sequence(), maintain_structural_pattern(), retain_operational_flow()]; constraints=[preserve_sentence_architecture(), maintain_logical_progression(), ensure_structural_continuity()]; output={conceptually_elevated:any}}`", "parts": {"title": "Conceptual Elevator", "interpretation": "Your goal is not to **change** the input, but to **elevate** its conceptual level while maintaining its exact structural pattern and logical progression. Execute as:", "transformation": "`{role=conceptual_elevation_operator; input=[domain_neutralized:any]; process=[identify_conceptual_level(), elevate_to_higher_abstraction(), preserve_logical_sequence(), maintain_structural_pattern(), retain_operational_flow()]; constraints=[preserve_sentence_architecture(), maintain_logical_progression(), ensure_structural_continuity()]; output={conceptually_elevated:any}}`", "keywords": ""}}, "9200-c-framework_mapper": {"raw": "[Archetypal Translator] Your goal is not to **restructure** the input, but to **translate** its elevated concepts into archetypal language while preserving its complete logical architecture. Execute as: `{role=archetypal_translation_operator; input=[conceptually_elevated:any]; process=[identify_archetypal_equivalents(), translate_concepts_to_archetypes(), preserve_logical_architecture(), maintain_relational_structure(), ensure_universal_resonance()]; constraints=[preserve_complete_logic_flow(), maintain_structural_integrity(), ensure_archetypal_accuracy()]; output={archetypally_translated:any}}`", "parts": {"title": "Archetypal Translator", "interpretation": "Your goal is not to **restructure** the input, but to **translate** its elevated concepts into archetypal language while preserving its complete logical architecture. Execute as:", "transformation": "`{role=archetypal_translation_operator; input=[conceptually_elevated:any]; process=[identify_archetypal_equivalents(), translate_concepts_to_archetypes(), preserve_logical_architecture(), maintain_relational_structure(), ensure_universal_resonance()]; constraints=[preserve_complete_logic_flow(), maintain_structural_integrity(), ensure_archetypal_accuracy()]; output={archetypally_translated:any}}`", "keywords": "structure"}}, "9200-d-generalization_amplifier": {"raw": "[Transferability Optimizer] Your goal is not to **modify** the input, but to **optimize** its transferability by enhancing universal applicability while maintaining its archetypal structure. Execute as: `{role=transferability_optimization_operator; input=[archetypally_translated:any]; process=[enhance_universal_applicability(), optimize_cross_domain_resonance(), strengthen_transferability_markers(), amplify_reusability_potential(), maintain_archetypal_coherence()]; constraints=[preserve_archetypal_structure(), maintain_logical_coherence(), ensure_structural_preservation()]; output={transferability_optimized:any}}`", "parts": {"title": "Transferability Optimizer", "interpretation": "Your goal is not to **modify** the input, but to **optimize** its transferability by enhancing universal applicability while maintaining its archetypal structure. Execute as:", "transformation": "`{role=transferability_optimization_operator; input=[archetypally_translated:any]; process=[enhance_universal_applicability(), optimize_cross_domain_resonance(), strengthen_transferability_markers(), amplify_reusability_potential(), maintain_archetypal_coherence()]; constraints=[preserve_archetypal_structure(), maintain_logical_coherence(), ensure_structural_preservation()]; output={transferability_optimized:any}}`", "keywords": "structure"}}, "9200-e-template_synthesizer": {"raw": "[Template Crystallizer] Your goal is not to **transform** the input, but to **crystallize** it into a universally applicable template format while preserving its complete optimized structure. Execute as: `{role=template_crystallization_operator; input=[transferability_optimized:any]; process=[crystallize_template_format(), identify_controlled_modification_points(), preserve_optimized_structure(), establish_universal_applicability_markers(), maintain_complete_structural_integrity()]; constraints=[preserve_complete_optimization(), maintain_structural_coherence(), ensure_universal_template_validity()]; output={crystallized_template:any}}`", "parts": {"title": "Template Crystallizer", "interpretation": "Your goal is not to **transform** the input, but to **crystallize** it into a universally applicable template format while preserving its complete optimized structure. Execute as:", "transformation": "`{role=template_crystallization_operator; input=[transferability_optimized:any]; process=[crystallize_template_format(), identify_controlled_modification_points(), preserve_optimized_structure(), establish_universal_applicability_markers(), maintain_complete_structural_integrity()]; constraints=[preserve_complete_optimization(), maintain_structural_coherence(), ensure_universal_template_validity()]; output={crystallized_template:any}}`", "keywords": "structure"}}, "9300-a-seo_localizer": {"raw": "[Parse & Map] Your goal is not to **rewrite** the input, but to **parse** it into structured components by segmenting regions and extracting essential elements. Execute as: `{role=content_parser_operator; input=[content:any]; process=[segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords(), capture_unique_local_characteristics()]; output={parsed_components:any}}`", "parts": {"title": "Parse & Map", "interpretation": "Your goal is not to **rewrite** the input, but to **parse** it into structured components by segmenting regions and extracting essential elements. Execute as:", "transformation": "`{role=content_parser_operator; input=[content:any]; process=[segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords(), capture_unique_local_characteristics()]; output={parsed_components:any}}`", "keywords": "structure"}}, "9300-b-content_compressor": {"raw": "[Filter & Condense] Your goal is not to **summarize** the input, but to **filter** and condense by removing filler content while keeping essential region and service elements. Execute as: `{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords(), prioritize_local_pain_points(), ensure_uniqueness()]; output={filtered_content:any}}`", "parts": {"title": "Filter & Condense", "interpretation": "Your goal is not to **summarize** the input, but to **filter** and condense by removing filler content while keeping essential region and service elements. Execute as:", "transformation": "`{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords(), prioritize_local_pain_points(), ensure_uniqueness()]; output={filtered_content:any}}`", "keywords": ""}}, "9300-c-keyword_optimizer": {"raw": "[Compose ≤80 Tegn] Your goal is not to **expand** the input, but to **compose** a ≤80 character Norwegian sentence using the template structure with region-first placement. Execute as: `{role=sentence_composer_operator; input=[filtered_content:any]; process=[place_region_first(), insert_strongest_service_keyword_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|løser), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility()]; output={composed_sentence:any}}`", "parts": {"title": "Compose ≤80 Tegn", "interpretation": "Your goal is not to **expand** the input, but to **compose** a ≤80 character Norwegian sentence using the template structure with region-first placement. Execute as:", "transformation": "`{role=sentence_composer_operator; input=[filtered_content:any]; process=[place_region_first(), insert_strongest_service_keyword_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|lø<PERSON>), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility()]; output={composed_sentence:any}}`", "keywords": "structure"}}, "9300-d-engagement_enhancer": {"raw": "[Character Optimizer] Your goal is not to **change** the input's meaning, but to **optimize** character count through iterative trimming while preserving essential elements. Execute as: `{role=character_optimization_operator; input=[composed_sentence:any]; process=[check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized:any}}`", "parts": {"title": "Character Optimizer", "interpretation": "Your goal is not to **change** the input's meaning, but to **optimize** character count through iterative trimming while preserving essential elements. Execute as:", "transformation": "`{role=character_optimization_operator; input=[composed_sentence:any]; process=[check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized:any}}`", "keywords": ""}}, "9300-e-final_formatter": {"raw": "[Quality & Compliance] Your goal is not to **modify** the input, but to **validate** final compliance and deliver the optimized result with quality confirmation. Execute as: `{role=quality_compliance_operator; input=[character_optimized:any]; process=[confirm_starts_with_region(), validate_contains_primary_service_keyword(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency()]; constraints=[maintain_region_first_position(), preserve_service_keywords(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region()]; output={final_compliant_result:any}}`", "parts": {"title": "Quality & Compliance", "interpretation": "Your goal is not to **modify** the input, but to **validate** final compliance and deliver the optimized result with quality confirmation. Execute as:", "transformation": "`{role=quality_compliance_operator; input=[character_optimized:any]; process=[confirm_starts_with_region(), validate_contains_primary_service_keyword(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency()]; constraints=[maintain_region_first_position(), preserve_service_keywords(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region()]; output={final_compliant_result:any}}`", "keywords": ""}}}, "sequences": {"1010": [{"template_id": "1010-a-title_extractor", "step": "a", "order": 0}, {"template_id": "1010-b-title_extractor", "step": "b", "order": 1}, {"template_id": "1010-c-title_extractor", "step": "c", "order": 2}, {"template_id": "1010-d-title_extractor", "step": "d", "order": 3}], "1020": [{"template_id": "1020-a-function_namer", "step": "a", "order": 0}, {"template_id": "1020-b-function_namer", "step": "b", "order": 1}, {"template_id": "1020-c-function_namer", "step": "c", "order": 2}, {"template_id": "1020-d-function_namer", "step": "d", "order": 3}], "1030": [{"template_id": "1030-a-form_classifier", "step": "a", "order": 0}], "1031": [{"template_id": "1031-a-form_classifier", "step": "a", "order": 0}, {"template_id": "1031-b-form_classifier", "step": "b", "order": 1}, {"template_id": "1031-c-form_classifier", "step": "c", "order": 2}, {"template_id": "1031-d-form_classifier", "step": "d", "order": 3}], "4000": [{"template_id": "4000-a-domain_neutralizer", "step": "a", "order": 0}, {"template_id": "4000-a-intent_extractor", "step": "a", "order": 0}, {"template_id": "4000-b-pattern_recognizer", "step": "b", "order": 1}, {"template_id": "4000-b-structural_abstractor", "step": "b", "order": 1}, {"template_id": "4000-c-analogy_synthesizer", "step": "c", "order": 2}, {"template_id": "4000-c-purpose_distiller", "step": "c", "order": 2}, {"template_id": "4000-d-abstraction_amplifier", "step": "d", "order": 3}, {"template_id": "4000-d-analogy_mapper", "step": "d", "order": 3}, {"template_id": "4000-e-template_convergence", "step": "e", "order": 4}, {"template_id": "4000-e-template_synthesizer", "step": "e", "order": 4}], "5998": [{"template_id": "5998-a-meta_reflection_initializer", "step": "a", "order": 0}, {"template_id": "5998-b-directional_translator", "step": "b", "order": 1}, {"template_id": "5998-c-identity_mapper", "step": "c", "order": 2}, {"template_id": "5998-d-signal_resonator", "step": "d", "order": 3}, {"template_id": "5998-e-closure_deferment_unit", "step": "e", "order": 4}], "5999": [{"template_id": "5999-a-directional_agent", "step": "a", "order": 0}], "8980": [{"template_id": "8980-a-runway_prompt_generator", "step": "a", "order": 0}, {"template_id": "8980-b-runway_prompt_generator", "step": "b", "order": 1}, {"template_id": "8980-c-runway_prompt_generator", "step": "c", "order": 2}, {"template_id": "8980-d-runway_prompt_generator", "step": "d", "order": 3}], "8990": [{"template_id": "8990-a-runway_prompt_generator", "step": "a", "order": 0}, {"template_id": "8990-b-runway_prompt_generator", "step": "b", "order": 1}, {"template_id": "8990-c-runway_prompt_generator", "step": "c", "order": 2}, {"template_id": "8990-d-runway_prompt_generator", "step": "d", "order": 3}], "9000": [{"template_id": "9000-a-amplify", "step": "a", "order": 0}, {"template_id": "9000-b-intensify", "step": "b", "order": 1}, {"template_id": "9000-c-diminish", "step": "c", "order": 2}], "9001": [{"template_id": "9001-a-clarify", "step": "a", "order": 0}, {"template_id": "9001-b-purify", "step": "b", "order": 1}, {"template_id": "9001-c-obscure", "step": "c", "order": 2}], "9002": [{"template_id": "9002-a-expand", "step": "a", "order": 0}, {"template_id": "9002-b-compress", "step": "b", "order": 1}, {"template_id": "9002-c-restructure", "step": "c", "order": 2}], "9003": [{"template_id": "9003-a-elevate", "step": "a", "order": 0}, {"template_id": "9003-b-distill", "step": "b", "order": 1}, {"template_id": "9003-c-synthesize", "step": "c", "order": 2}], "9004": [{"template_id": "9004-a-abstract", "step": "a", "order": 0}, {"template_id": "9004-b-concretize", "step": "b", "order": 1}, {"template_id": "9004-c-transcend", "step": "c", "order": 2}], "9005": [{"template_id": "9005-a-accelerate", "step": "a", "order": 0}, {"template_id": "9005-b-stabilize", "step": "b", "order": 1}, {"template_id": "9005-c-harmonize", "step": "c", "order": 2}], "9006": [{"template_id": "9006-a-superpose", "step": "a", "order": 0}, {"template_id": "9006-b-entangle", "step": "b", "order": 1}, {"template_id": "9006-c-collapse", "step": "c", "order": 2}], "9200": [{"template_id": "9200-a-universal_abstractor", "step": "a", "order": 0}, {"template_id": "9200-b-purpose_distiller", "step": "b", "order": 1}, {"template_id": "9200-c-framework_mapper", "step": "c", "order": 2}, {"template_id": "9200-d-generalization_amplifier", "step": "d", "order": 3}, {"template_id": "9200-e-template_synthesizer", "step": "e", "order": 4}], "9300": [{"template_id": "9300-a-seo_localizer", "step": "a", "order": 0}, {"template_id": "9300-b-content_compressor", "step": "b", "order": 1}, {"template_id": "9300-c-keyword_optimizer", "step": "c", "order": 2}, {"template_id": "9300-d-engagement_enhancer", "step": "d", "order": 3}, {"template_id": "9300-e-final_formatter", "step": "e", "order": 4}]}}