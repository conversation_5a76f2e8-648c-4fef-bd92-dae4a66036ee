[Pattern Recognizer] Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as: `{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`