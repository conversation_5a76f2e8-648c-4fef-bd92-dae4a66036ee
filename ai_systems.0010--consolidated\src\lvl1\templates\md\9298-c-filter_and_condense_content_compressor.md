[Technical Expertise Integration] Your goal is not to **summarize** the input, but to **integrate** SEO-verified technical expertise and filter content while preserving validated service keywords. Execute as: `{role=technical_expertise_operator; input=[parsed_components_with_services:any]; process=[add_seo_verified_technical_expertise(), remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services_from_verified_taxonomy_ONLY(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords_from_verified_taxonomy_ONLY(), prioritize_local_pain_points(), ensure_uniqueness(), reject_non_verified_service_terms()]; output={filtered_content_with_expertise:any}}`

**SEO VERIFIED TECHNICAL EXPERTISE:**
**SCHEMA TECHNICAL TERMS**: fiberduk, bærelag, set<PERSON><PERSON>, platevibrator, frostfritt fundament, geonett, d<PERSON><PERSON><PERSON><PERSON><PERSON>, mineralsk jord, jordf<PERSON>er, rotbløyte, cortenst<PERSON><PERSON>, granitt, naturstein
**CLIMATE ADAPTATION**: "tilpasset norsk klima", "lokale forhold", "Ringerikes unike terreng og klima", "tåler frost og tele"
