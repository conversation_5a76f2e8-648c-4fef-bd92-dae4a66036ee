[Technical Expertise Integration] Your goal is not to **summarize** the input, but to **integrate** technical expertise and filter content while preserving validated service keywords. Execute as: `{role=technical_expertise_operator; input=[parsed_components_with_services:any]; process=[add_technical_expertise(), remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services_from_taxonomy_ONLY(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords_from_taxonomy_ONLY(), prioritize_local_pain_points(), ensure_uniqueness(), reject_non_taxonomy_service_terms()]; output={filtered_content_with_expertise:any}}`

**TECHNICAL EXPERTISE REFERENCE:**
**E-A-T SIGNALS**: fiberduk, bærelag, settesand, platevibrator, frostfritt fundament, geonett, d<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>k jord, jordf<PERSON>er, rot<PERSON><PERSON><PERSON><PERSON>, corten<PERSON><PERSON><PERSON>, granitt, naturstein
