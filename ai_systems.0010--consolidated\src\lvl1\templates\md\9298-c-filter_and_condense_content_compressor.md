[Filter & Condense (Content Compressor)] Your goal is not to **summarize** the input, but to **filter** and condense while preserving only reference-validated service keywords. Execute as: `{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services_from_reference_ONLY(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords_from_reference_ONLY(), prioritize_local_pain_points(), ensure_uniqueness(), reject_non_reference_service_terms()]; output={filtered_content:any}}`
