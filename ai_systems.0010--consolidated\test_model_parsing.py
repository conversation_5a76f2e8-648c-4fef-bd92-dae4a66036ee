#!/usr/bin/env python3
"""
Test script for the new model specification parsing functionality.
"""

import sys
import os

# Add the src directory to the path so we can import the module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'lvl1'))

from lvl1_sequence_executor import PromptParser

def test_model_extraction():
    """Test the model extraction functionality."""
    
    print("Testing Model Extraction Functionality")
    print("=" * 50)
    
    test_cases = [
        # Test case 1: Basic model specification
        {
            "input": "[MODEL:gpt-3.5-turbo|gpt-4.1] [SEQ:1031] Test prompt",
            "expected_models": ["gpt-3.5-turbo", "gpt-4.1"],
            "expected_sequence": "1031",
            "expected_prompt": "Test prompt"
        },
        
        # Test case 2: Model specification with comma separator
        {
            "input": "[MODEL:gpt-3.5-turbo,gpt-4.1] Test prompt with comma",
            "expected_models": ["gpt-3.5-turbo", "gpt-4.1"],
            "expected_sequence": None,
            "expected_prompt": "Test prompt with comma"
        },
        
        # Test case 3: Multiple models with sequence
        {
            "input": "[MODEL:gpt-4o|claude-3-sonnet|gemini-pro] [SEQ:0001:a-c] Complex test",
            "expected_models": ["gpt-4o", "claude-3-sonnet", "gemini-pro"],
            "expected_sequence": "0001:a-c",
            "expected_prompt": "Complex test"
        },
        
        # Test case 4: No model specification
        {
            "input": "[SEQ:1031] Just sequence test",
            "expected_models": None,
            "expected_sequence": "1031",
            "expected_prompt": "Just sequence test"
        },
        
        # Test case 5: Model at end
        {
            "input": "Test prompt [MODEL:gpt-4.1]",
            "expected_models": ["gpt-4.1"],
            "expected_sequence": None,
            "expected_prompt": "Test prompt"
        },
        
        # Test case 6: Alternative syntax with --models
        {
            "input": "Test prompt --models=gpt-3.5-turbo,gpt-4.1",
            "expected_models": ["gpt-3.5-turbo", "gpt-4.1"],
            "expected_sequence": None,
            "expected_prompt": "Test prompt"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        print(f"Input: {test_case['input']}")
        
        # Test the extraction
        cleaned_prompt, sequence_spec, models_list = PromptParser.extract_all_from_prompt(test_case['input'])
        
        print(f"Extracted prompt: '{cleaned_prompt}'")
        print(f"Extracted sequence: {sequence_spec}")
        print(f"Extracted models: {models_list}")
        
        # Validate results
        success = True
        
        if cleaned_prompt != test_case['expected_prompt']:
            print(f"❌ FAIL: Expected prompt '{test_case['expected_prompt']}', got '{cleaned_prompt}'")
            success = False
            
        if sequence_spec != test_case['expected_sequence']:
            print(f"❌ FAIL: Expected sequence '{test_case['expected_sequence']}', got '{sequence_spec}'")
            success = False
            
        if models_list != test_case['expected_models']:
            print(f"❌ FAIL: Expected models {test_case['expected_models']}, got {models_list}")
            success = False
            
        if success:
            print("✅ PASS")
        
        print("-" * 30)

def test_validation():
    """Test the validation functions."""
    
    print("\nTesting Validation Functions")
    print("=" * 50)
    
    # Test sequence validation
    sequence_tests = [
        ("1031", True),
        ("0001:a-c", True),
        ("1031|9000", True),
        ("keyword:distill", True),
        ("invalid", False),
        ("", False),
        (None, False)
    ]
    
    print("\nSequence Validation Tests:")
    for seq, expected in sequence_tests:
        result = PromptParser.validate_sequence_spec(seq)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"  {seq} -> {result} (expected {expected}) {status}")
    
    # Test model validation
    model_tests = [
        (["gpt-3.5-turbo", "gpt-4.1"], True),
        (["gpt-4o"], True),
        ([], False),
        ([""], False),
        (["gpt-4o", ""], False),
        (None, False),
        ("not-a-list", False)
    ]
    
    print("\nModel Validation Tests:")
    for models, expected in model_tests:
        result = PromptParser.validate_model_list(models)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"  {models} -> {result} (expected {expected}) {status}")

def test_execution_behavior():
    """Test that the execution behavior works as expected."""
    print("\nTesting Execution Behavior")
    print("=" * 50)

    print("\nExpected behavior with [MODEL:gpt-3.5-turbo|gpt-4.1] [SEQ:1031:a-b]:")
    print("1. Step 'a' runs through gpt-3.5-turbo first, then gpt-4.1")
    print("2. Step 'b' uses gpt-4.1's output from step 'a' as input")
    print("3. Step 'b' runs through gpt-3.5-turbo first, then gpt-4.1")
    print("4. Final result is gpt-4.1's output from step 'b'")

    print("\nThis ensures each step processes through ALL models before proceeding to the next step.")
    print("The last model's output becomes the input for the next step in chain mode.")

if __name__ == "__main__":
    test_model_extraction()
    test_validation()
    test_execution_behavior()
    print("\n" + "=" * 50)
    print("Test completed!")
