[Analogy Mapper] Your goal is not to **retain** specific examples, but to **map** all concrete references to high-level, transferable analogies. Execute as: `{role=universal_analogy_generator; input=[distilled_purpose:str]; process=[identify_concrete_references(), extract_underlying_patterns(), generate_archetypal_analogies(), create_transferable_metaphors(), establish_universal_conceptual_bridges()]; constraints=[ensure_analogy_universality(), maintain_conceptual_accuracy(), preserve_relational_dynamics()]; requirements=[domain_agnostic_analogies(), archetypal_metaphors(), universal_conceptual_frameworks()]; output={analogized_framework:str}}`