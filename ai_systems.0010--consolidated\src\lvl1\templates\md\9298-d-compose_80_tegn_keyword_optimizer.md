[Brand Voice Integration] Your goal is not to **expand** the input, but to **integrate** SEO-verified brand voice and compose a ≤80 character Norwegian sentence using validated keywords and template structure. Execute as: `{role=brand_voice_composer_operator; input=[filtered_content_with_expertise:any]; process=[add_seo_verified_brand_voice(), place_region_first(), insert_strongest_service_keyword_from_verified_taxonomy_within_40_chars(), use_verified_active_verbs(bygger|leverer|skaper|tilbyr), insert_verified_geo_specific_adjective_if_space(terrengrikt|fjordnært|lokale), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility(), use_verified_taxonomy_service_keywords_ONLY()]; output={composed_sentence_with_brand:any}}`

**WEBSITE VERIFIED COPY TEMPLATE:**
**VERB**: leverer · bygger · fornyer · skaper
**ADJ (kort)**: terrengrikt · fjordnært
**TEMPLATE (≤ 80 tegn)**: <Region>: <Service/Benefit> – lokal anleggsgartner
