[Brand Voice Integration] Your goal is not to **expand** the input, but to **integrate** brand voice and compose a ≤80 character Norwegian sentence using validated keywords and template structure. Execute as: `{role=brand_voice_composer_operator; input=[filtered_content_with_expertise:any]; process=[add_brand_voice(), place_region_first(), insert_strongest_service_keyword_from_taxonomy_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|løser), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility(), use_taxonomy_service_keywords_ONLY()]; output={composed_sentence_with_brand:any}}`

**BRAND VOICE REFERENCE:**
**ACTIVE VERBS**: bygger | leverer | fornyer | lø<PERSON> | skaper | utfører | sikrer
**GEO-SPECIFIC**: terrengrikt | fjordnært | sentral | etablert
