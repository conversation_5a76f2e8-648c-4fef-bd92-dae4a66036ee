[Compose ≤80 Tegn (Keyword Optimizer)] Your goal is not to **expand** the input, but to **compose** a ≤80 character Norwegian sentence using reference-validated keywords and template structure. Execute as: `{role=sentence_composer_operator; input=[filtered_content:any]; process=[place_region_first(), insert_strongest_service_keyword_from_reference_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|løser), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility(), use_reference_service_keywords_ONLY()]; output={composed_sentence:any}}`
