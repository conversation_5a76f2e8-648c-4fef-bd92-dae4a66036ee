[Character Optimizer (Engagement Enhancer)] Your goal is not to **change** the input's meaning, but to **optimize** character count through iterative trimming while preserving reference-validated service keywords. Execute as: `{role=character_optimization_operator; input=[composed_sentence:any]; process=[check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords_from_reference_ONLY(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized:any}}`
