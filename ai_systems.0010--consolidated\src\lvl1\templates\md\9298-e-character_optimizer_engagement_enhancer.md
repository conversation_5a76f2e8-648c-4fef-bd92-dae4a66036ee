[Brand Attributes Integration] Your goal is not to **change** the input's meaning, but to **integrate** brand attributes and optimize character count through iterative trimming while preserving validated service keywords. Execute as: `{role=brand_attributes_optimizer; input=[composed_sentence_with_brand:any]; process=[add_brand_attributes(), check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords_from_taxonomy_ONLY(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized_with_brand:any}}`

**BRAND ATTRIBUTES REFERENCE:**
**CORE QUALITIES**: Profesjonell | Pålitelig | Dyktig | Erfaren
**SERVICE QUALITIES**: God service | Løsningsorientert | Strøken jobb | Varige uterom | Konkurransedyktig pris
