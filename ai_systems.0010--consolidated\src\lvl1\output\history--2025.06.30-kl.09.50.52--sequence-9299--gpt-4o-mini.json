  {
  "initial_prompt": "Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.",
  "sequence_id": "9299",
  "results": [
      {
      "instruction": "# Service Reference\n\nYour goal is not to **analyze** the input, but to **prepend** a concise summary list of all Ringerike Landskap services for consistent reference throughout the SEO process. Execute as:\n\n`{role=service_reference_operator; input=[content:any]; process=[prepend_ringerike_landskap_service_list(), create_seo_keyword_reference(), establish_service_priority_hierarchy(), maintain_original_content_intact()]; constraints=[preserve_complete_original_input(), ensure_service_list_accuracy(), maintain_norwegian_terminology()]; output={content_with_service_reference:any}}`",
      "step": "a",
      "title": "Service Reference",
      "input": "Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "```json\n{\n  \"content_with_service_reference\": {\n    \"service_reference\": [\n      \"Anleggsgartner-tjenester\",\n      \"Maskinentreprenørtjenester\",\n      \"Hagearbeid\",\n      \"Gårdsplassdesign\",\n      \"Uteområdeservice\"\n    ],\n    \"original_content\": \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Parse & Map\n\nYour goal is not to **rewrite** the input, but to **parse** it into structured components by segmenting regions and extracting essential elements using the service reference list. Execute as:\n\n`{role=content_parser_operator; input=[content_with_service_reference:any]; process=[segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services_from_reference_list(støttemur|belegningsstein|ferdigplen|drenering|cortenstål|kantstein|hekk|beplantning|platting|trapper|repoer), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords_from_reference(), capture_unique_local_characteristics(), use_service_reference_for_consistency()]; output={parsed_components:any}}`",
      "step": "b",
      "title": "Parse & Map",
      "input": "\n'```[Initial Prompt]: \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"```'\n\n```json\n{\n  \"content_with_service_reference\": {\n    \"service_reference\": [\n      \"Anleggsgartner-tjenester\",\n      \"Maskinentreprenørtjenester\",\n      \"Hagearbeid\",\n      \"Gårdsplassdesign\",\n      \"Uteområdeservice\"\n    ],\n    \"original_content\": \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"\n  }\n}\n```",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "```json\n{\n  \"parsed_components\": {\n    \"regions\": [\n      {\n        \"name\": \"Røyse\",\n        \"services_offered\": [\n          \"Anleggsgartner-tjenester\",\n          \"Maskinentreprenørtjenester\",\n          \"Hagearbeid\",\n          \"Gårdsplassdesign\",\n          \"Uteområdeservice\"\n        ],\n        \"local_details\": {\n          \"terrain\": \"nærområdet\",\n          \"climate\": \"n/a\",\n          \"distance\": \"kort kjøretid\"\n        }\n      }\n    ],\n    \"summary\": \"Vår base med daglig tilstedeværelse tilbyr komplette tjenester for hage, gårdsplass og uteområder.\"\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Filter & Condense\n\nYour goal is not to **summarize** the input, but to **filter** and condense by removing filler content while keeping essential region and service elements from the reference list. Execute as:\n\n`{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services_from_reference(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords_from_reference(), prioritize_local_pain_points(), ensure_uniqueness(), reference_complete_service_list()]; output={filtered_content:any}}`",
      "step": "c",
      "title": "Filter & Condense",
      "input": "\n'```[Initial Prompt]: \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"```'\n\n```json\n{\n  \"parsed_components\": {\n    \"regions\": [\n      {\n        \"name\": \"Røyse\",\n        \"services_offered\": [\n          \"Anleggsgartner-tjenester\",\n          \"Maskinentreprenørtjenester\",\n          \"Hagearbeid\",\n          \"Gårdsplassdesign\",\n          \"Uteområdeservice\"\n        ],\n        \"local_details\": {\n          \"terrain\": \"nærområdet\",\n          \"climate\": \"n/a\",\n          \"distance\": \"kort kjøretid\"\n        }\n      }\n    ],\n    \"summary\": \"Vår base med daglig tilstedeværelse tilbyr komplette tjenester for hage, gårdsplass og uteområder.\"\n  }\n}\n```",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "```json\n{\n  \"filtered_content\": {\n    \"regions\": [\n      {\n        \"name\": \"Røyse\",\n        \"services_offered\": [\n          \"Anleggsgartner-tjenester\",\n          \"Maskinentreprenørtjenester\"\n        ]\n      }\n    ]\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Compose ≤80 Tegn\n\nYour goal is not to **expand** the input, but to **compose** a ≤80 character Norwegian sentence using the template structure with region-first placement and service reference consistency. Execute as:\n\n`{role=sentence_composer_operator; input=[filtered_content:any]; process=[place_region_first(), insert_strongest_service_keyword_from_reference_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|løser), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility(), ensure_service_reference_consistency()]; output={composed_sentence:any}}`",
      "step": "d",
      "title": "Compose ≤80 Tegn",
      "input": "\n'```[Initial Prompt]: \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"```'\n\n```json\n{\n  \"filtered_content\": {\n    \"regions\": [\n      {\n        \"name\": \"Røyse\",\n        \"services_offered\": [\n          \"Anleggsgartner-tjenester\",\n          \"Maskinentreprenørtjenester\"\n        ]\n      }\n    ]\n  }\n}\n```",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "```json\n{\n  \"composed_sentence\": \"Røyse_anleggsgartner_lokal_anleggsgartner\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Character Optimizer\n\nYour goal is not to **change** the input's meaning, but to **optimize** character count through iterative trimming while preserving essential service reference elements. Execute as:\n\n`{role=character_optimization_operator; input=[composed_sentence:any]; process=[check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords_from_reference(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized:any}}`",
      "step": "e",
      "title": "Character Optimizer",
      "input": "\n'```[Initial Prompt]: \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"```'\n\n```json\n{\n  \"composed_sentence\": \"Røyse_anleggsgartner_lokal_anleggsgartner\"\n}\n```",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "```json\n{\n  \"character_optimized\": \"Røyse - Vår base med kort kjøretid tilbyr anleggsgartnertjenester.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Quality & Compliance\n\nYour goal is not to **modify** the input, but to **validate** final compliance and deliver the optimized result with quality confirmation and service reference verification. Execute as:\n\n`{role=quality_compliance_operator; input=[character_optimized:any]; process=[confirm_starts_with_region(), validate_contains_primary_service_keyword_from_reference(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency(), verify_service_reference_consistency()]; constraints=[maintain_region_first_position(), preserve_service_keywords_from_reference(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region()]; output={final_compliant_result:any}}`",
      "step": "f",
      "title": "Quality & Compliance",
      "input": "\n'```[Initial Prompt]: \"Røyse - Hovedområde - Vår base med kort kjøretid og daglig tilstedeværelse - Vi tilbyr komplette anleggsgartner- og maskinentreprenørtjenester for hage, gårdsplass og uteområder i nærområdet.\"```'\n\n```json\n{\n  \"character_optimized\": \"Røyse - Vår base med kort kjøretid tilbyr anleggsgartnertjenester.\"\n}\n```",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "```json\n{\n  \"final_compliant_result\": \"Røyse - Vår base med kort kjøretid tilbyr anleggsgartnertjenester.\"\n}\n```",
        }
      }
    }