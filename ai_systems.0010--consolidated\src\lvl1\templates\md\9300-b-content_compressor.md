[Filter & Condense] Your goal is not to **summarize** the input, but to **filter** and condense by removing filler content while keeping essential region and service elements. Execute as: `{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords(), prioritize_local_pain_points(), ensure_uniqueness()]; output={filtered_content:any}}`
