[Final SEO Validation] Your goal is not to **modify** the input, but to **validate** final SEO compliance and deliver the optimized result with complete company reference verification. Execute as: `{role=seo_validation_operator; input=[character_optimized_with_brand:any]; process=[add_final_seo_validation(), confirm_starts_with_region(), validate_contains_primary_service_keyword_from_taxonomy_ONLY(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency(), validate_company_consistency()]; constraints=[maintain_region_first_position(), preserve_service_keywords_from_taxonomy_ONLY(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region(), reject_non_taxonomy_service_terms()]; output={final_seo_compliant_result:any}}`

**FINAL SEO VALIDATION REFERENCE:**
**COMPLIANCE CHECKLIST**: Region first | Service keyword from taxonomy | ≤80 characters | Norwegian fluency | Company consistency | Uniqueness guarantee
