[Final SEO Validation] Your goal is not to **modify** the input, but to **validate** final SEO compliance against verified website audit data and deliver the optimized result. Execute as: `{role=seo_validation_operator; input=[character_optimized_with_brand:any]; process=[add_seo_audit_validation(), confirm_starts_with_region(), validate_contains_primary_service_keyword_from_verified_taxonomy_ONLY(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency(), validate_schema_consistency()]; constraints=[maintain_region_first_position(), preserve_service_keywords_from_verified_taxonomy_ONLY(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region(), reject_non_verified_service_terms()]; output={final_seo_audit_compliant_result:any}}`

**WEBSITE VERIFIED COMPLIANCE GUARDRAIL:**
**MUST REQUIREMENTS**: Må starte med region · inneholde ≥1 CORE-tjeneste · ≤ 80 tegn
**QUALITY CHECK**: Sjekk duplikat mot andre region-linjer · norsk, aktiv form
**FINAL VALIDATION**: 100% faktabaserte utdrag fra nettstedet
