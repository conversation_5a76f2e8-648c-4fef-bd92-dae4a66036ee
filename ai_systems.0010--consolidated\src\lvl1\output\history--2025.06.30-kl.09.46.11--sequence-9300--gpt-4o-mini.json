  {
  "initial_prompt": "Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.",
  "sequence_id": "9300",
  "results": [
      {
      "instruction": "# Parse & Map\n\nYour goal is not to **rewrite** the input, but to **parse** it into structured components by segmenting regions and extracting essential elements. Execute as:\n\n`{role=content_parser_operator; input=[content:any]; process=[segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords(), capture_unique_local_characteristics()]; output={parsed_components:any}}`",
      "step": "a",
      "title": "Parse & Map",
      "input": "Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"parsed_components\": {\n    \"regions\": [\n      {\n        \"name\": \"Vik\",\n        \"description\": \"Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring.\",\n        \"services_offered\": [],\n        \"local_details\": {\n          \"terrain\": \"fruktbar jord\",\n          \"climate\": \"fjordnær beliggenhet\",\n          \"distance\": null\n        }\n      }\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Filter & Condense\n\nYour goal is not to **summarize** the input, but to **filter** and condense by removing filler content while keeping essential region and service elements. Execute as:\n\n`{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords(), prioritize_local_pain_points(), ensure_uniqueness()]; output={filtered_content:any}}`",
      "step": "b",
      "title": "Filter & Condense",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"parsed_components\": {\n    \"regions\": [\n      {\n        \"name\": \"Vik\",\n        \"description\": \"Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring.\",\n        \"services_offered\": [],\n        \"local_details\": {\n          \"terrain\": \"fruktbar jord\",\n          \"climate\": \"fjordnær beliggenhet\",\n          \"distance\": null\n        }\n      }\n    ]\n  }\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"filtered_content\": {\n    \"regions\": [\n      {\n        \"name\": \"Vik\",\n        \"services_offered\": [\n          \"drenering\"\n        ]\n      }\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Compose ≤80 Tegn\n\nYour goal is not to **expand** the input, but to **compose** a ≤80 character Norwegian sentence using the template structure with region-first placement. Execute as:\n\n`{role=sentence_composer_operator; input=[filtered_content:any]; process=[place_region_first(), insert_strongest_service_keyword_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|løser), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility()]; output={composed_sentence:any}}`",
      "step": "c",
      "title": "Compose ≤80 Tegn",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"filtered_content\": {\n    \"regions\": [\n      {\n        \"name\": \"Vik\",\n        \"services_offered\": [\n          \"drenering\"\n        ]\n      }\n    ]\n  }\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"composed_sentence\": \"Vik_drenering_lokal_anleggsgartner\"\n}",
        }
      }
    },
      {
      "instruction": "# Character Optimizer\n\nYour goal is not to **change** the input's meaning, but to **optimize** character count through iterative trimming while preserving essential elements. Execute as:\n\n`{role=character_optimization_operator; input=[composed_sentence:any]; process=[check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized:any}}`",
      "step": "d",
      "title": "Character Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"composed_sentence\": \"Vik_drenering_lokal_anleggsgartner\"\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"character_optimized\": \"Vik drenering lokal gartner\"\n}",
        }
      }
    },
      {
      "instruction": "# Quality & Compliance\n\nYour goal is not to **modify** the input, but to **validate** final compliance and deliver the optimized result with quality confirmation. Execute as:\n\n`{role=quality_compliance_operator; input=[character_optimized:any]; process=[confirm_starts_with_region(), validate_contains_primary_service_keyword(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency()]; constraints=[maintain_region_first_position(), preserve_service_keywords(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region()]; output={final_compliant_result:any}}`",
      "step": "e",
      "title": "Quality & Compliance",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"character_optimized\": \"Vik drenering lokal gartner\"\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"final_compliant_result\": \"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\"\n}",
        }
      }
    }