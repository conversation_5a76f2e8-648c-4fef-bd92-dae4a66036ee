[Service Taxonomy Integration] Your goal is not to **rewrite** the input, but to **integrate** SEO-verified service taxonomy and parse regional content with strict keyword validation. Execute as: `{role=service_taxonomy_operator; input=[content:any]; process=[add_seo_verified_service_taxonomy(), segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services_ONLY_from_verified_taxonomy(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords_from_verified_taxonomy_ONLY(), capture_unique_local_characteristics(), exclude_non_verified_service_terms()]; output={parsed_components_with_services:any}}`

**WEBSITE VERIFIED SERVICE TAXONOMY:**
**H1 FROM WEBSITE**: "Anleggsgartner & maskinentreprenør" | "Anleggsgartnertjenester i Ringerike"
**WEBSITE VERIFIED SERVICES**: Belegningsstein | Cortenstål | Støttemurer | Platting | Ferdigplen | Kantstein | Trapper og Repoer | Hekk og Beplantning
**WEBSITE META KEYWORDS**: anleggsgartner, hagedesign, belegningsstein, støttemur, landskapsarkitektur, Ringerike, Hole, Røyse, Hønefoss
