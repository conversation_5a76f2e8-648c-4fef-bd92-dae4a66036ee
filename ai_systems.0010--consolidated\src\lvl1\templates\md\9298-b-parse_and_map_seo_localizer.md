[Service Taxonomy Integration] Your goal is not to **rewrite** the input, but to **integrate** service taxonomy and parse regional content with strict keyword validation. Execute as: `{role=service_taxonomy_operator; input=[content:any]; process=[add_service_taxonomy(), segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services_ONLY_from_taxonomy(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords_from_taxonomy_ONLY(), capture_unique_local_characteristics(), exclude_non_taxonomy_service_terms()]; output={parsed_components_with_services:any}}`

**SERVICE TAXONOMY REFERENCE:**
**PRIMARY SERVICES**: Anleggsgartner | Grunnarbeid | Maskinentreprenør | Landskapsutforming
**CORE SERVICES**: Belegningsstein/Steinlegging | Støttemur | Ferdigplen | Drenering | Platting/Terrasse | Trapper og Repoer | Kantstein | Hekk og Beplantning | Riving og Sanering
