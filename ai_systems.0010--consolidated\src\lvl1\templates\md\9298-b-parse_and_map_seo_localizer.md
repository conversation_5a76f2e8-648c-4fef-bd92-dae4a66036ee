[Parse & Map (SEO Localizer)] Your goal is not to **rewrite** the input, but to **parse** and map regional content using strict service keyword selection from the company reference. Execute as: `{role=content_parser_operator; input=[content:any]; process=[segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services_ONLY_from_reference(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords_from_reference_ONLY(), capture_unique_local_characteristics(), exclude_non_reference_service_terms()]; output={parsed_components:any}}`
