  {
  "initial_prompt": "Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.",
  "sequence_id": "9298",
  "results": [
      {
      "instruction": "# Company Reference Injection\n\nYour goal is not to **analyze** the input, but to **prepend** the exact company reference block for consistent SEO processing. Execute as:\n\n`{role=company_reference_operator; input=[content:any]; process=[prepend_company_reference(), maintain_original_content_intact()]; constraints=[preserve_complete_original_input(), use_exact_company_information_block()]; output={content_with_company_reference:any}}`",
      "step": "a",
      "title": "Company Reference Injection",
      "input": "Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"content_with_company_reference\": \"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\"\n}",
        }
      }
    },
      {
      "instruction": "# Parse & Map (SEO Localizer)\n\nYour goal is not to **rewrite** the input, but to **parse** and map regional content using strict service keyword selection from the company reference. Execute as:\n\n`{role=content_parser_operator; input=[content:any]; process=[segment_input_into_region_blocks(), extract_region_labels(), identify_ringerike_services_ONLY_from_reference(støttemur|belegningsstein|ferdigplen|drenering|cortenstål), extract_local_details(terrain|climate|distance)]; constraints=[preserve_region_names_verbatim(), maintain_service_keywords_from_reference_ONLY(), capture_unique_local_characteristics(), exclude_non_reference_service_terms()]; output={parsed_components:any}}`",
      "step": "b",
      "title": "Parse & Map (SEO Localizer)",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"content_with_company_reference\": \"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\"\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"parsed_components\": {\n    \"regions\": [\n      {\n        \"region_name\": \"Vik\",\n        \"local_details\": {\n          \"terrain\": \"fruktbar jord\",\n          \"climate\": \"fjordnær beliggenhet\",\n          \"distance\": null\n        },\n        \"services\": [\n          \"drenering\"\n        ]\n      }\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Filter & Condense (Content Compressor)\n\nYour goal is not to **summarize** the input, but to **filter** and condense while preserving only reference-validated service keywords. Execute as:\n\n`{role=content_filter_operator; input=[parsed_components:any]; process=[remove_filler_adjectives(), eliminate_repeated_phrases(), remove_distances_and_years_unless_unique(), keep_region_words_verbatim(), preserve_max_2_high_value_services_from_reference_ONLY(støttemur|belegningsstein|ferdigplen|drenering|cortenstål)]; constraints=[maintain_region_names_exactly(), preserve_service_keywords_from_reference_ONLY(), prioritize_local_pain_points(), ensure_uniqueness(), reject_non_reference_service_terms()]; output={filtered_content:any}}`",
      "step": "c",
      "title": "Filter & Condense (Content Compressor)",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"parsed_components\": {\n    \"regions\": [\n      {\n        \"region_name\": \"Vik\",\n        \"local_details\": {\n          \"terrain\": \"fruktbar jord\",\n          \"climate\": \"fjordnær beliggenhet\",\n          \"distance\": null\n        },\n        \"services\": [\n          \"drenering\"\n        ]\n      }\n    ]\n  }\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"filtered_content\": {\n    \"regions\": [\n      {\n        \"region_name\": \"Vik\",\n        \"services\": [\n          \"drenering\"\n        ]\n      }\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Compose ≤80 Tegn (Keyword Optimizer)\n\nYour goal is not to **expand** the input, but to **compose** a ≤80 character Norwegian sentence using reference-validated keywords and template structure. Execute as:\n\n`{role=sentence_composer_operator; input=[filtered_content:any]; process=[place_region_first(), insert_strongest_service_keyword_from_reference_within_40_chars(), use_active_verbs(bygger|leverer|fornyer|løser), insert_geo_specific_adjective_if_space(terrengrikt|fjordnært), apply_template_structure(<Region>_<Service/Benefit>_lokal_anleggsgartner)]; constraints=[stay_under_80_characters(), prioritize_first_40_chars(), use_norwegian_active_voice(), maintain_template_flexibility(), use_reference_service_keywords_ONLY()]; output={composed_sentence:any}}`",
      "step": "d",
      "title": "Compose ≤80 Tegn (Keyword Optimizer)",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"filtered_content\": {\n    \"regions\": [\n      {\n        \"region_name\": \"Vik\",\n        \"services\": [\n          \"drenering\"\n        ]\n      }\n    ]\n  }\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"composed_sentence\": \"Vik_drenering_lokal_anleggsgartner\"\n}",
        }
      }
    },
      {
      "instruction": "# Character Optimizer (Engagement Enhancer)\n\nYour goal is not to **change** the input's meaning, but to **optimize** character count through iterative trimming while preserving reference-validated service keywords. Execute as:\n\n`{role=character_optimization_operator; input=[composed_sentence:any]; process=[check_character_count(), trim_weak_adjectives_if_over_80(), swap_long_words_for_norwegian_synonyms(profesjonelle→proff), remove_dash_secondary_clause_last_resort(), maintain_region_first_position()]; constraints=[stay_under_80_characters(), preserve_region_name(), maintain_service_keywords_from_reference_ONLY(), keep_active_voice(), ensure_norwegian_fluency()]; output={character_optimized:any}}`",
      "step": "e",
      "title": "Character Optimizer (Engagement Enhancer)",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"composed_sentence\": \"Vik_drenering_lokal_anleggsgartner\"\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"character_optimized\": \"Vik drenering lokal anleggsgartner\"\n}",
        }
      }
    },
      {
      "instruction": "# Quality & Compliance (Final Formatter)\n\nYour goal is not to **modify** the input, but to **validate** final compliance and deliver the optimized result with reference-validated service keyword verification. Execute as:\n\n`{role=quality_compliance_operator; input=[character_optimized:any]; process=[confirm_starts_with_region(), validate_contains_primary_service_keyword_from_reference_ONLY(), verify_under_80_characters_including_spaces(), ensure_no_identical_sentences(), confirm_norwegian_fluency()]; constraints=[maintain_region_first_position(), preserve_service_keywords_from_reference_ONLY(), ensure_character_limit_compliance(), guarantee_uniqueness(), deliver_single_line_per_region(), reject_non_reference_service_terms()]; output={final_compliant_result:any}}`",
      "step": "f",
      "title": "Quality & Compliance (Final Formatter)",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Fruktbar jord og fjordnær beliggenhet stiller krav til god drenering og riktig plantevalg.\\\"```'\\n\\n\",\n  \"character_optimized\": \"Vik drenering lokal anleggsgartner\"\n}",
      "responses": {
          "gpt-4o-mini": {
          "model": "gpt-4o-mini",
          "content": "{\n  \"final_compliant_result\": \"Vik - Vik er en naturlig del av vårt nærområde i Hole, der vi jevnlig bistår både private og næring. Dreneringstillit krever god drenering og rett plantevalg.\"\n}",
        }
      }
    }