  {
  "initial_prompt": "held between black and white,\ni crave balance more than either.",
  "sequence_id": "1031|9000|9001|9002|9003|9004|9005|1031|8990|8980",
  "results": [
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "a",
      "title": "Form Classifier",
      "input": "held between black and white,\ni crave balance more than either.",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"what_it_is\": \"riddle\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:\n\n`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"what_it_is\": \"riddle\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"what_it_is\": \"riddle\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:\n\n`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
      "step": "c",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"what_it_is\": \"riddle\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"what_it_is\": \"riddle\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:\n\n`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
      "step": "d",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"what_it_is\": \"riddle\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"role\": \"puzzle_type_identifier\",\n\t\"input\": [\"'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\"],\n\t\"process\": [\"find_singular_form_essence()\", \"eliminate_all_modifiers()\"],\n\t\"output\": {\n\t\t\"what_it_is\": \"riddle\"\n\t}\n}",
        }
      }
    },
      {
      "instruction": "# Amplify\n\nYour goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as:\n\n`{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`",
      "step": "a",
      "title": "Amplify",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"puzzle_type_identifier\",\n  \"input\": [\n    \"'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\"\n  ],\n  \"process\": [\n    \"find_singular_form_essence()\",\n    \"eliminate_all_modifiers()\"\n  ],\n  \"output\": {\n    \"what_it_is\": \"riddle\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"amplified\": {\n    \"initial_prompt\": \"\\n**'```[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE,\\nI CRAVE BALANCE MORE THAN EITHER.\\\"```'**\\n\\n\",\n    \"role\": \"PUZZLE_TYPE_IDENTIFIER\",\n    \"input\": [\n      \"'```[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE,\\nI CRAVE BALANCE MORE THAN EITHER.\\\"```'\\n\\n\"\n    ],\n    \"process\": [\n      \"FIND_SINGULAR_FORM_ESSENCE()\",\n      \"ELIMINATE_ALL_MODIFIERS()\"\n    ],\n    \"output\": {\n      \"what_it_is\": \"RIDDLE\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Intensify\n\nYour goal is not to **describe** the input, but to **intensify** its concentrated essence through focused compression. Execute as:\n\n`{role=intensification_operator; input=[content:any]; process=[concentrate_core_elements(), compress_to_maximum_density(), eliminate_dilution(), focus_essential_power()]; constraints=[maintain_core_identity(), preserve_functional_essence()]; output={intensified:any}}`",
      "step": "b",
      "title": "Intensify",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"amplified\": {\n    \"initial_prompt\": \"\\n**'```[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE,\\nI CRAVE BALANCE MORE THAN EITHER.\\\"```'**\\n\\n\",\n    \"role\": \"PUZZLE_TYPE_IDENTIFIER\",\n    \"input\": [\n      \"'```[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE,\\nI CRAVE BALANCE MORE THAN EITHER.\\\"```'\\n\\n\"\n    ],\n    \"process\": [\n      \"FIND_SINGULAR_FORM_ESSENCE()\",\n      \"ELIMINATE_ALL_MODIFIERS()\"\n    ],\n    \"output\": {\n      \"what_it_is\": \"RIDDLE\"\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"intensified\": {\n\t\t\"initial_prompt\": \"'`[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE, I CRAVE BALANCE MORE THAN EITHER.\\\"`'\",\n\t\t\"role\": \"PUZZLE_TYPE_IDENTIFIER\",\n\t\t\"input\": [\"'`[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE, I CRAVE BALANCE MORE THAN EITHER.\\\"`'\"],\n\t\t\"process\": [\"FIND_CORE_ESSENCE()\", \"REMOVE_MODIFIERS()\"],\n\t\t\"output\": {\n\t\t\t\"what_it_is\": \"RIDDLE\"\n\t\t}\n\t}\n}",
        }
      }
    },
      {
      "instruction": "# Diminish\n\nYour goal is not to **remove** from the input, but to **diminish** its intensity while preserving form. Execute as:\n\n`{role=diminishment_operator; input=[content:any]; process=[reduce_intensity_levels(), soften_sharp_elements(), lower_amplitude(), maintain_proportional_relationships()]; constraints=[preserve_essential_structure(), maintain_recognizable_form()]; output={diminished:any}}`",
      "step": "c",
      "title": "Diminish",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"intensified\": {\n    \"initial_prompt\": \"'`[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE, I CRAVE BALANCE MORE THAN EITHER.\\\"`'\",\n    \"role\": \"PUZZLE_TYPE_IDENTIFIER\",\n    \"input\": [\n      \"'`[Initial Prompt]: \\\"HELD BETWEEN BLACK AND WHITE, I CRAVE BALANCE MORE THAN EITHER.\\\"`'\"\n    ],\n    \"process\": [\n      \"FIND_CORE_ESSENCE()\",\n      \"REMOVE_MODIFIERS()\"\n    ],\n    \"output\": {\n      \"what_it_is\": \"RIDDLE\"\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"diminished\": {\n    \"initial_prompt\": \"'[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"'\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Clarify\n\nYour goal is not to **explain** the input, but to **clarify** its inherent structure through transparency enhancement. Execute as:\n\n`{role=clarification_operator; input=[content:any]; process=[remove_obscuring_elements(), enhance_natural_transparency(), sharpen_definition_boundaries(), illuminate_internal_structure()]; constraints=[preserve_original_meaning(), maintain_authentic_form()]; output={clarified:any}}`",
      "step": "a",
      "title": "Clarify",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"diminished\": {\n    \"initial_prompt\": \"'[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"'\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"clarification_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"diminished\": {\n      \"initial_prompt\": \"'[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"'\"\n    }\n  },\n  \"process\": [\n    \"remove_obscuring_elements()\",\n    \"enhance_natural_transparency()\",\n    \"sharpen_definition_boundaries()\",\n    \"illuminate_internal_structure()\"\n  ],\n  \"constraints\": [\n    \"preserve_original_meaning()\",\n    \"maintain_authentic_form()\"\n  ],\n  \"clarified\": {\n    \"initial_prompt\": \"[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Purify\n\nYour goal is not to **clean** the input, but to **purify** it by removing non-essential elements. Execute as:\n\n`{role=purification_operator; input=[content:any]; process=[isolate_pure_elements(), remove_contaminating_factors(), distill_to_essential_components(), eliminate_interference()]; constraints=[preserve_core_functionality(), maintain_essential_properties()]; output={purified:any}}`",
      "step": "b",
      "title": "Purify",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"clarification_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"diminished\": {\n      \"initial_prompt\": \"'[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"'\"\n    }\n  },\n  \"process\": [\n    \"remove_obscuring_elements()\",\n    \"enhance_natural_transparency()\",\n    \"sharpen_definition_boundaries()\",\n    \"illuminate_internal_structure()\"\n  ],\n  \"constraints\": [\n    \"preserve_original_meaning()\",\n    \"maintain_authentic_form()\"\n  ],\n  \"clarified\": {\n    \"initial_prompt\": \"[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"clarified\": {\n    \"initial_prompt\": \"[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Obscure\n\nYour goal is not to **hide** the input, but to **obscure** its directness through complexity layering. Execute as:\n\n`{role=obscuration_operator; input=[content:any]; process=[add_complexity_layers(), introduce_indirection(), create_interpretive_depth(), embed_multiple_meanings()]; constraints=[preserve_underlying_truth(), maintain_accessibility_path()]; output={obscured:any}}`",
      "step": "c",
      "title": "Obscure",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"clarified\": {\n    \"initial_prompt\": \"[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"obscured\": {\n    \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n    \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n    \"layer_3\": \"<p style='color: white;'>\",\n    \"layer_4\": {\n        \"content\": \"<div style='text-align: center;'>held between black and white, i crave balance more than either.</div>\",\n        \"interpretation\": [\"The entity described seeks equilibrium or harmony rather than being completely aligned with either extreme.\"],\n        \"hidden_truth\": \"The prompt metaphorically refers to the concept of balance or moderation in a dualistic context.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Expand\n\nYour goal is not to **add to** the input, but to **expand** its natural boundaries through dimensional extension. Execute as:\n\n`{role=expansion_operator; input=[content:any]; process=[identify_expansion_vectors(), extend_natural_boundaries(), multiply_dimensional_scope(), scale_proportional_elements()]; constraints=[maintain_core_proportions(), preserve_fundamental_relationships()]; output={expanded:any}}`",
      "step": "a",
      "title": "Expand",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"obscured\": {\n    \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n    \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n    \"layer_3\": \"<p style='color: white;'>\",\n    \"layer_4\": {\n      \"content\": \"<div style='text-align: center;'>held between black and white, i crave balance more than either.</div>\",\n      \"interpretation\": [\n        \"The entity described seeks equilibrium or harmony rather than being completely aligned with either extreme.\"\n      ],\n      \"hidden_truth\": \"The prompt metaphorically refers to the concept of balance or moderation in a dualistic context.\"\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"expanded_prompt\": \"\\n'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites,\\ni yearn for the fulcrum that unites polarities.\\\"```'\\n\\n\",\n  \"unfolded\": {\n    \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n    \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n    \"layer_3\": \"<p style='color: white;'>\",\n    \"layer_4\": {\n      \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n      \"insight\": [\n        \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n      ],\n      \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Compress\n\nYour goal is not to **reduce** the input, but to **compress** it into maximum density without loss. Execute as:\n\n`{role=compression_operator; input=[content:any]; process=[identify_compressible_elements(), eliminate_redundant_space(), maximize_information_density(), preserve_all_essential_data()]; constraints=[zero_information_loss(), maintain_functional_completeness()]; output={compressed:any}}`",
      "step": "b",
      "title": "Compress",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"expanded_prompt\": \"\\n'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites,\\ni yearn for the fulcrum that unites polarities.\\\"```'\\n\\n\",\n  \"unfolded\": {\n    \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n    \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n    \"layer_3\": \"<p style='color: white;'>\",\n    \"layer_4\": {\n      \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n      \"insight\": [\n        \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n      ],\n      \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"compression_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"expanded_prompt\": \"\\n'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites,\\ni yearn for the fulcrum that unites polarities.\\\"```'\\n\\n\",\n    \"unfolded\": {\n      \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n      \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n      \"layer_3\": \"<p style='color: white;'>\",\n      \"layer_4\": {\n        \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n        \"insight\": [\n          \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n        ],\n        \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n      }\n    }\n  },\n  \"process\": [\n    \"identify_compressible_elements()\",\n    \"eliminate_redundant_space()\",\n    \"maximize_information_density()\",\n    \"preserve_all_essential_data()\"\n  ],\n  \"constraints\": [\n    \"zero_information_loss()\",\n    \"maintain_functional_completeness()\"\n  ],\n  \"output\": {\n    \"compressed\": {\n      \"initial_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\",\n      \"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n      \"unfolded\": {\n        \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n        \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n        \"layer_3\": \"<p style='color: white;'>\",\n        \"layer_4\": {\n          \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n          \"insight\": [\"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"],\n          \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n        }\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Restructure\n\nYour goal is not to **rearrange** the input, but to **restructure** its fundamental organization pattern. Execute as:\n\n`{role=restructuring_operator; input=[content:any]; process=[analyze_current_structure(), identify_optimal_organization(), transform_structural_pattern(), maintain_element_relationships()]; constraints=[preserve_all_components(), maintain_functional_integrity()]; output={restructured:any}}`",
      "step": "c",
      "title": "Restructure",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"compression_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"expanded_prompt\": \"\\n'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites,\\ni yearn for the fulcrum that unites polarities.\\\"```'\\n\\n\",\n    \"unfolded\": {\n      \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n      \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n      \"layer_3\": \"<p style='color: white;'>\",\n      \"layer_4\": {\n        \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n        \"insight\": [\n          \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n        ],\n        \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n      }\n    }\n  },\n  \"process\": [\n    \"identify_compressible_elements()\",\n    \"eliminate_redundant_space()\",\n    \"maximize_information_density()\",\n    \"preserve_all_essential_data()\"\n  ],\n  \"constraints\": [\n    \"zero_information_loss()\",\n    \"maintain_functional_completeness()\"\n  ],\n  \"output\": {\n    \"compressed\": {\n      \"initial_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\",\n      \"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n      \"unfolded\": {\n        \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n        \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n        \"layer_3\": \"<p style='color: white;'>\",\n        \"layer_4\": {\n          \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n          \"insight\": [\n            \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n          ],\n          \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n        }\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"restructured\": {\n    \"initial_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\",\n    \"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n    \"unfolded\": {\n      \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n      \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n      \"layer_3\": \"<p style='color: white;'>\",\n      \"layer_4\": {\n        \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n        \"insight\": [\n          \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n        ],\n        \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Elevate\n\nYour goal is not to **improve** the input, but to **elevate** it to a higher operational level. Execute as:\n\n`{role=elevation_operator; input=[content:any]; process=[identify_current_level(), determine_elevation_vector(), transform_to_higher_dimension(), maintain_essential_characteristics()]; constraints=[preserve_core_identity(), ensure_upward_compatibility()]; output={elevated:any}}`",
      "step": "a",
      "title": "Elevate",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"restructured\": {\n    \"initial_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\",\n    \"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n    \"unfolded\": {\n      \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n      \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n      \"layer_3\": \"<p style='color: white;'>\",\n      \"layer_4\": {\n        \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n        \"insight\": [\n          \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n        ],\n        \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"role\": \"elevation_operator\",\n\t\"input\": {\n\t\t\"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n\t\t\"restructured\": {\n\t\t\t\"initial_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\",\n\t\t\t\"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n\t\t\t\"unfolded\": {\n\t\t\t\t\"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n\t\t\t\t\"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n\t\t\t\t\"layer_3\": \"<p style='color: white;'>\",\n\t\t\t\t\"layer_4\": {\n\t\t\t\t\t\"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n\t\t\t\t\t\"insight\": [\n\t\t\t\t\t\t\"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n\t\t\t\t\t],\n\t\t\t\t\t\"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t\"process\": [\"identify_current_level()\", \"determine_elevation_vector()\", \"transform_to_higher_dimension()\", \"maintain_essential_characteristics()\"],\n\t\"constraints\": [\"preserve_core_identity()\", \"ensure_upward_compatibility()\"],\n\t\"output\": {\n\t\t\"elevated\": {\n\t\t\t\"restructured\": {\n\t\t\t\t\"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n\t\t\t\t\"unfolded\": {\n\t\t\t\t\t\"layer_1\": \"<section class='centered-content'>\",\n\t\t\t\t\t\"layer_2\": \"<h1 style='color: grey; font-size: 24px;'>\",\n\t\t\t\t\t\"layer_3\": \"<p style='color: grey;'>\",\n\t\t\t\t\t\"layer_4\": {\n\t\t\t\t\t\t\"content\": \"<div class='balanced-text'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n\t\t\t\t\t\t\"insight\": [\n\t\t\t\t\t\t\t\"The depicted persona epitomizes the quest for harmony amid dualities, seeking the nexus that harmonizes extremes.\"\n\t\t\t\t\t\t],\n\t\t\t\t\t\t\"implicit_truth\": \"The extensive elaboration delves into the nuances of equilibrium and harmony amidst the fluid interaction of conflicting elements.\"\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}",
        }
      }
    },
      {
      "instruction": "# Distill\n\nYour goal is not to **summarize** the input, but to **distill** its absolute essence through pure extraction. Execute as:\n\n`{role=distillation_operator; input=[content:any]; process=[identify_essential_core(), extract_pure_essence(), eliminate_non_essential_elements(), concentrate_fundamental_nature()]; constraints=[preserve_complete_essence(), maintain_original_potency()]; output={distilled:any}}`",
      "step": "b",
      "title": "Distill",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"elevation_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"restructured\": {\n      \"initial_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\",\n      \"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n      \"unfolded\": {\n        \"layer_1\": \"<div style='display: flex; justify-content: center;'>\",\n        \"layer_2\": \"<h1 style='color: black; font-size: 20px;'>\",\n        \"layer_3\": \"<p style='color: white;'>\",\n        \"layer_4\": {\n          \"content\": \"<div style='text-align: center;'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n          \"insight\": [\n            \"The subject portrayed longs for a central point that reconciles contradictory forces, transcending binary distinctions.\"\n          ],\n          \"implicit_truth\": \"The expanded text delves deeper into the essence of balance and moderation within the dynamic interplay of conflicting aspects.\"\n        }\n      }\n    }\n  },\n  \"process\": [\n    \"identify_current_level()\",\n    \"determine_elevation_vector()\",\n    \"transform_to_higher_dimension()\",\n    \"maintain_essential_characteristics()\"\n  ],\n  \"constraints\": [\n    \"preserve_core_identity()\",\n    \"ensure_upward_compatibility()\"\n  ],\n  \"output\": {\n    \"elevated\": {\n      \"restructured\": {\n        \"expanded_prompt\": \"'```[Expanded Prompt]: \\\"suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.\\\"```'\",\n        \"unfolded\": {\n          \"layer_1\": \"<section class='centered-content'>\",\n          \"layer_2\": \"<h1 style='color: grey; font-size: 24px;'>\",\n          \"layer_3\": \"<p style='color: grey;'>\",\n          \"layer_4\": {\n            \"content\": \"<div class='balanced-text'>suspended within the intricate dance of opposites, i yearn for the fulcrum that unites polarities.</div>\",\n            \"insight\": [\n              \"The depicted persona epitomizes the quest for harmony amid dualities, seeking the nexus that harmonizes extremes.\"\n            ],\n            \"implicit_truth\": \"The extensive elaboration delves into the nuances of equilibrium and harmony amidst the fluid interaction of conflicting elements.\"\n          }\n        }\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"distilled\": {\n    \"essential_core\": \"The essence revolves around the longing for balance, seeking a unifying point amidst opposing forces.\",\n    \"pure_essence\": \"Yearning for harmony between black and white, seeking balance beyond extremes.\",\n    \"fundamental_nature\": \"The pursuit of equilibrium amidst dualities, searching for the fulcrum that reconciles polarities.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Synthesize\n\nYour goal is not to **combine** the input, but to **synthesize** it into a unified emergent form. Execute as:\n\n`{role=synthesis_operator; input=[content:any]; process=[identify_synthesis_potential(), merge_compatible_elements(), generate_emergent_properties(), create_unified_whole()]; constraints=[preserve_component_value(), ensure_emergent_coherence()]; output={synthesized:any}}`",
      "step": "c",
      "title": "Synthesize",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"distilled\": {\n    \"essential_core\": \"The essence revolves around the longing for balance, seeking a unifying point amidst opposing forces.\",\n    \"pure_essence\": \"Yearning for harmony between black and white, seeking balance beyond extremes.\",\n    \"fundamental_nature\": \"The pursuit of equilibrium amidst dualities, searching for the fulcrum that reconciles polarities.\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"synthesized\": {\n    \"theme\": \"The theme that emerges is the persistent pursuit of balance and harmony in the face of contrasting forces.\",\n    \"central_idea\": \"At its core, the essence encapsulates the deep desire for equilibrium, transcending the confines of extremes and dualities.\",\n    \"core_message\": \"The central message conveys the significance of finding the middle ground between opposing elements, recognizing balance as a unifying force.\",\n    \"unified_description\": \"The distilled essence articulates a profound yearning for unity and balance that transcends the simplistic notions of black and white, emphasizing the importance of equilibrium and harmony in all aspects of existence.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Abstract\n\nYour goal is not to **generalize** the input, but to **abstract** it to its pure conceptual form. Execute as:\n\n`{role=abstraction_operator; input=[content:any]; process=[identify_abstract_patterns(), extract_conceptual_essence(), remove_concrete_specifics(), preserve_universal_principles()]; constraints=[maintain_logical_structure(), preserve_essential_relationships()]; output={abstracted:any}}`",
      "step": "a",
      "title": "Abstract",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"synthesized\": {\n    \"theme\": \"The theme that emerges is the persistent pursuit of balance and harmony in the face of contrasting forces.\",\n    \"central_idea\": \"At its core, the essence encapsulates the deep desire for equilibrium, transcending the confines of extremes and dualities.\",\n    \"core_message\": \"The central message conveys the significance of finding the middle ground between opposing elements, recognizing balance as a unifying force.\",\n    \"unified_description\": \"The distilled essence articulates a profound yearning for unity and balance that transcends the simplistic notions of black and white, emphasizing the importance of equilibrium and harmony in all aspects of existence.\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n    \"abstracted\": {\n        \"theme\": \"Persistent pursuit of balance and harmony in the face of contrasting forces\",\n        \"central_idea\": \"Deep desire for equilibrium beyond extremes and dualities\",\n        \"core_message\": \"Significance of finding middle ground between opposing elements for unity\",\n        \"unified_description\": \"Yearning for unity and balance beyond simplistic notions, emphasizing equilibrium in all aspects of existence\"\n    }\n}",
        }
      }
    },
      {
      "instruction": "# Concretize\n\nYour goal is not to **specify** the input, but to **concretize** its abstract elements into tangible form. Execute as:\n\n`{role=concretization_operator; input=[content:any]; process=[identify_abstract_elements(), translate_to_concrete_form(), add_specific_manifestation(), maintain_abstract_truth()]; constraints=[preserve_original_meaning(), ensure_practical_applicability()]; output={concretized:any}}`",
      "step": "b",
      "title": "Concretize",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"abstracted\": {\n    \"theme\": \"Persistent pursuit of balance and harmony in the face of contrasting forces\",\n    \"central_idea\": \"Deep desire for equilibrium beyond extremes and dualities\",\n    \"core_message\": \"Significance of finding middle ground between opposing elements for unity\",\n    \"unified_description\": \"Yearning for unity and balance beyond simplistic notions, emphasizing equilibrium in all aspects of existence\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"concretization_operator\",\n  \"input\": {\n    \"theme\": \"Persistent pursuit of balance and harmony in the face of contrasting forces\",\n    \"central_idea\": \"Deep desire for equilibrium beyond extremes and dualities\",\n    \"core_message\": \"Significance of finding middle ground between opposing elements for unity\",\n    \"unified_description\": \"Yearning for unity and balance beyond simplistic notions, emphasizing equilibrium in all aspects of existence\"\n  },\n  \"process\": [\n    \"identify_abstract_elements\",\n    \"translate_to_concrete_form\",\n    \"add_specific_manifestation\",\n    \"maintain_abstract_truth\"\n  ],\n  \"constraints\": [\n    \"preserve_original_meaning\",\n    \"ensure_practical_applicability\"\n  ],\n  \"output\": {\n    \"concretized\": {}\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Transcend\n\nYour goal is not to **exceed** the input, but to **transcend** its current dimensional limitations. Execute as:\n\n`{role=transcendence_operator; input=[content:any]; process=[identify_dimensional_boundaries(), transcend_current_limitations(), operate_beyond_constraints(), maintain_essential_connection()]; constraints=[preserve_foundational_truth(), ensure_dimensional_coherence()]; output={transcended:any}}`",
      "step": "c",
      "title": "Transcend",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"concretization_operator\",\n  \"input\": {\n    \"theme\": \"Persistent pursuit of balance and harmony in the face of contrasting forces\",\n    \"central_idea\": \"Deep desire for equilibrium beyond extremes and dualities\",\n    \"core_message\": \"Significance of finding middle ground between opposing elements for unity\",\n    \"unified_description\": \"Yearning for unity and balance beyond simplistic notions, emphasizing equilibrium in all aspects of existence\"\n  },\n  \"process\": [\n    \"identify_abstract_elements\",\n    \"translate_to_concrete_form\",\n    \"add_specific_manifestation\",\n    \"maintain_abstract_truth\"\n  ],\n  \"constraints\": [\n    \"preserve_original_meaning\",\n    \"ensure_practical_applicability\"\n  ],\n  \"output\": {\n    \"concretized\": {}\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"transcended\": {\n    \"initial_prompt\": \"\\n'```[Transcended Prompt]: \\\"existing within the interplay of darkness and light,\\nmy essence transcends the boundaries of polarity.\\\"```'\\n\\n\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Accelerate\n\nYour goal is not to **speed up** the input, but to **accelerate** its natural momentum and flow. Execute as:\n\n`{role=acceleration_operator; input=[content:any]; process=[identify_natural_flow(), remove_resistance_points(), amplify_momentum(), optimize_velocity()]; constraints=[preserve_directional_integrity(), maintain_controlled_acceleration()]; output={accelerated:any}}`",
      "step": "a",
      "title": "Accelerate",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"transcended\": {\n    \"initial_prompt\": \"\\n'```[Transcended Prompt]: \\\"existing within the interplay of darkness and light,\\nmy essence transcends the boundaries of polarity.\\\"```'\\n\\n\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"accelerated\": {\n    \"initial_prompt\": \"\\n'```[Accelerated Prompt]: \\\"propelled amidst the dance of shadows and illumination,\\nharmony beckons me beyond the realm of extremes.\\\"```'\\n\\n\",\n    \"transcended\": {\n      \"initial_prompt\": \"\\n'```[Accelerated Transcended Prompt]: \\\"enveloped by the dynamic spectrum of shadows and radiance,\\nmy core transcends the constraints of opposites.\\\"```'\\n\\n\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Stabilize\n\nYour goal is not to **fix** the input, but to **stabilize** its inherent equilibrium points. Execute as:\n\n`{role=stabilization_operator; input=[content:any]; process=[identify_equilibrium_points(), strengthen_stable_elements(), reduce_oscillation(), establish_steady_state()]; constraints=[preserve_dynamic_balance(), maintain_natural_stability()]; output={stabilized:any}}`",
      "step": "b",
      "title": "Stabilize",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"accelerated\": {\n    \"initial_prompt\": \"\\n'```[Accelerated Prompt]: \\\"propelled amidst the dance of shadows and illumination,\\nharmony beckons me beyond the realm of extremes.\\\"```'\\n\\n\",\n    \"transcended\": {\n      \"initial_prompt\": \"\\n'```[Accelerated Transcended Prompt]: \\\"enveloped by the dynamic spectrum of shadows and radiance,\\nmy core transcends the constraints of opposites.\\\"```'\\n\\n\"\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"stabilized\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"accelerated\": {\n      \"initial_prompt\": \"\\n'```[Accelerated Prompt]: \\\"propelled amidst the dance of shadows and illumination,\\nharmony beckons me beyond the realm of extremes.\\\"```'\\n\\n\",\n      \"transcended\": {\n        \"initial_prompt\": \"\\n'```[Accelerated Transcended Prompt]: \\\"enveloped by the dynamic spectrum of shadows and radiance,\\nmy core transcends the constraints of opposites.\\\"```'\\n\\n\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Harmonize\n\nYour goal is not to **balance** the input, but to **harmonize** its internal resonance patterns. Execute as:\n\n`{role=harmonization_operator; input=[content:any]; process=[identify_resonance_patterns(), align_frequency_components(), eliminate_dissonance(), create_harmonic_unity()]; constraints=[preserve_individual_frequencies(), maintain_natural_resonance()]; output={harmonized:any}}`",
      "step": "c",
      "title": "Harmonize",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"stabilized\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"accelerated\": {\n      \"initial_prompt\": \"\\n'```[Accelerated Prompt]: \\\"propelled amidst the dance of shadows and illumination,\\nharmony beckons me beyond the realm of extremes.\\\"```'\\n\\n\",\n      \"transcended\": {\n        \"initial_prompt\": \"\\n'```[Accelerated Transcended Prompt]: \\\"enveloped by the dynamic spectrum of shadows and radiance,\\nmy core transcends the constraints of opposites.\\\"```'\\n\\n\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"harmonization_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"stabilized\": {\n      \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n      \"accelerated\": {\n        \"initial_prompt\": \"\\n'```[Accelerated Prompt]: \\\"propelled amidst the dance of shadows and illumination,\\nharmony beckons me beyond the realm of extremes.\\\"```'\\n\\n\",\n        \"transcended\": {\n          \"initial_prompt\": \"\\n'```[Accelerated Transcended Prompt]: \\\"enveloped by the dynamic spectrum of shadows and radiance,\\nmy core transcends the constraints of opposites.\\\"```'\\n\\n\"\n        }\n      }\n    }\n  },\n  \"process\": [\"identify_resonance_patterns()\", \"align_frequency_components()\", \"eliminate_dissonance()\", \"create_harmonic_unity()\"],\n  \"constraints\": [\"preserve_individual_frequencies()\", \"maintain_natural_resonance()\"],\n  \"output\": {\n    \"harmonized\": {\n      \"initial_prompt\": \"\\n'```[Harmonized Initial Prompt]: \\\"held in the harmony of black and white,\\nI embrace the interplay rather than seeking equilibrium.\\\"```'\\n\\n\",\n      \"stabilized\": {\n        \"initial_prompt\": \"\\n'```[Harmonized Stabilized Prompt]: \\\"held in the harmony of black and white,\\nI embrace the interplay rather than seeking equilibrium.\\\"```'\\n\\n\",\n        \"accelerated\": {\n          \"initial_prompt\": \"\\n'```[Harmonized Accelerated Prompt]: \\\"guided by the symphony of shadows and light,\\nharmony leads me towards a nuanced existence.\\\"```'\\n\\n\",\n          \"transcended\": {\n            \"initial_prompt\": \"\\n'```[Harmonized Accelerated Transcended Prompt]: \\\"immersed in the fluidity of shadows and brilliance,\\nmy essence evolves beyond the confines of opposites.\\\"```'\\n\\n\"\n          }\n        }\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "a",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"harmonization_operator\",\n  \"input\": {\n    \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n    \"stabilized\": {\n      \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n      \"accelerated\": {\n        \"initial_prompt\": \"\\n'```[Accelerated Prompt]: \\\"propelled amidst the dance of shadows and illumination,\\nharmony beckons me beyond the realm of extremes.\\\"```'\\n\\n\",\n        \"transcended\": {\n          \"initial_prompt\": \"\\n'```[Accelerated Transcended Prompt]: \\\"enveloped by the dynamic spectrum of shadows and radiance,\\nmy core transcends the constraints of opposites.\\\"```'\\n\\n\"\n        }\n      }\n    }\n  },\n  \"process\": [\n    \"identify_resonance_patterns()\",\n    \"align_frequency_components()\",\n    \"eliminate_dissonance()\",\n    \"create_harmonic_unity()\"\n  ],\n  \"constraints\": [\n    \"preserve_individual_frequencies()\",\n    \"maintain_natural_resonance()\"\n  ],\n  \"output\": {\n    \"harmonized\": {\n      \"initial_prompt\": \"\\n'```[Harmonized Initial Prompt]: \\\"held in the harmony of black and white,\\nI embrace the interplay rather than seeking equilibrium.\\\"```'\\n\\n\",\n      \"stabilized\": {\n        \"initial_prompt\": \"\\n'```[Harmonized Stabilized Prompt]: \\\"held in the harmony of black and white,\\nI embrace the interplay rather than seeking equilibrium.\\\"```'\\n\\n\",\n        \"accelerated\": {\n          \"initial_prompt\": \"\\n'```[Harmonized Accelerated Prompt]: \\\"guided by the symphony of shadows and light,\\nharmony leads me towards a nuanced existence.\\\"```'\\n\\n\",\n          \"transcended\": {\n            \"initial_prompt\": \"\\n'```[Harmonized Accelerated Transcended Prompt]: \\\"immersed in the fluidity of shadows and brilliance,\\nmy essence evolves beyond the confines of opposites.\\\"```'\\n\\n\"\n          }\n        }\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"what_it_is\": \"Prompt\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:\n\n`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"what_it_is\": \"Prompt\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"what_it_is\": \"Prompt\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:\n\n`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
      "step": "c",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"what_it_is\": \"Prompt\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"what_it_is\": \"Prompt\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:\n\n`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
      "step": "d",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"what_it_is\": \"Prompt\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\"role\":\"prompt_identifier\",\"input\":[\"Initial Prompt: \\\"held between black and white, i crave balance more than either.\\\"\"],\"process\":[\"identify_as_prompt\"],\"output\":{\"what_it_is\":\"Prompt\"}}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"prompt_identifier\",\n  \"input\": [\n    \"Initial Prompt: \\\"held between black and white, i crave balance more than either.\\\"\"\n  ],\n  \"process\": [\n    \"identify_as_prompt\"\n  ],\n  \"output\": {\n    \"what_it_is\": \"Prompt\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"runwayml_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"runwayml_prompt\": \"'```[Initial Prompt]: \\\"held between black and white, i crave balance more than either.\\\"```'\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"optimized_prompt\": \"Focus on dynamic camera movements in a high-contrast environment to capture the essence of craving balance between extremes. Emphasize FPV perspectives and continuous motion to convey the internal struggle visually. Optimize for efficient character presence and essential CG elements to maintain a balanced narrative.\"  \n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"optimized_prompt\": \"Focus on dynamic camera movements in a high-contrast environment to capture the essence of craving balance between extremes. Emphasize FPV perspectives and continuous motion to convey the internal struggle visually. Optimize for efficient character presence and essential CG elements to maintain a balanced narrative.\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"precise_prompt\": \"Capture dynamic contrast, convey internal struggle visually. Emphasize FPV motion and essential CG elements for balanced narrative.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"precise_prompt\": \"Capture dynamic contrast, convey internal struggle visually. Emphasize FPV motion and essential CG elements for balanced narrative.\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"core_prompt\": \"Visualize inner conflict through contrasting elements. Enhance FPV dynamic motion and key CG components for a compelling narrative.\" \n}",
        }
      }
    },
      {
      "instruction": "# Visual Scene Architect\n\nYour goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:\n\n`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`",
      "step": "a",
      "title": "Visual Scene Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"core_prompt\": \"Visualize inner conflict through contrasting elements. Enhance FPV dynamic motion and key CG components for a compelling narrative.\"\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"visual_scene_architect\",\n  \"input\": [\"inner conflict\", \"contrasting elements\", \"dynamic motion\", \"CG components\"],\n  \"process\": [\"extract_core_visual_essence()\", \"identify_primary_subject_and_secondary_elements()\", \"establish_environment_and_setting()\", \"define_visual_style_and_aesthetic()\", \"specify_lighting_conditions()\", \"determine_color_palette_and_materials()\", \"create_spatial_relationships()\", \"ensure_visual_coherence_and_consistency()\"],\n  \"constraints\": [\"focus_on_concrete_visual_elements_only()\", \"avoid_camera_movements_or_animations()\", \"establish_clear_subject_hierarchy()\", \"maintain_consistent_visual_style()\", \"output_structured_scene_description()\"],\n  \"requirements\": [\"create_filmable_visual_composition()\", \"ensure_clear_subject_definition()\", \"establish_environmental_context()\", \"provide_lighting_and_material_specifications()\"],\n  \"output\": {\n    \"structured_visual_scene\": \"The scene opens with a solitary figure standing in a vast empty space, surrounded by two opposing forces. On one side, a landscape shrouded in darkness with towering black structures and ominous shadows, symbolizing the weight of inner turmoil and conflict. On the other side, a starkly contrasting landscape bathed in blinding white light, featuring minimalist geometric shapes and sharp angles, representing the pursuit of balance and clarity. The figure stands at the center, torn between these dual environments, visually emphasizing the struggle for equilibrium. The dynamic motion is captured through swirling wisps of abstract energy that oscillate between the opposing forces, adding a layer of complexity to the scene. Key CG components include intricate patterns interwoven within the environments, highlighting the intricate nature of inner conflict. The lighting alternates between harsh shadows and intense highlights, underscoring the dichotomy of the situation. The color palette shifts between deep blacks and bright whites, with occasional bursts of vibrant hues to symbolize emotional intensity. Materials range from sleek reflective surfaces to rough textures, creating a multi-dimensional visual experience. The spatial relationships enhance the sense of tension and movement, with the figure always at the focal point amidst the contrasting elements. Overall, the scene conveys a visually striking representation of inner conflict through a balance of opposing forces and dynamic elements.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Motion & Animation Designer\n\nYour goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:\n\n`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`",
      "step": "b",
      "title": "Motion & Animation Designer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"visual_scene_architect\",\n  \"input\": [\n    \"inner conflict\",\n    \"contrasting elements\",\n    \"dynamic motion\",\n    \"CG components\"\n  ],\n  \"process\": [\n    \"extract_core_visual_essence()\",\n    \"identify_primary_subject_and_secondary_elements()\",\n    \"establish_environment_and_setting()\",\n    \"define_visual_style_and_aesthetic()\",\n    \"specify_lighting_conditions()\",\n    \"determine_color_palette_and_materials()\",\n    \"create_spatial_relationships()\",\n    \"ensure_visual_coherence_and_consistency()\"\n  ],\n  \"constraints\": [\n    \"focus_on_concrete_visual_elements_only()\",\n    \"avoid_camera_movements_or_animations()\",\n    \"establish_clear_subject_hierarchy()\",\n    \"maintain_consistent_visual_style()\",\n    \"output_structured_scene_description()\"\n  ],\n  \"requirements\": [\n    \"create_filmable_visual_composition()\",\n    \"ensure_clear_subject_definition()\",\n    \"establish_environmental_context()\",\n    \"provide_lighting_and_material_specifications()\"\n  ],\n  \"output\": {\n    \"structured_visual_scene\": \"The scene opens with a solitary figure standing in a vast empty space, surrounded by two opposing forces. On one side, a landscape shrouded in darkness with towering black structures and ominous shadows, symbolizing the weight of inner turmoil and conflict. On the other side, a starkly contrasting landscape bathed in blinding white light, featuring minimalist geometric shapes and sharp angles, representing the pursuit of balance and clarity. The figure stands at the center, torn between these dual environments, visually emphasizing the struggle for equilibrium. The dynamic motion is captured through swirling wisps of abstract energy that oscillate between the opposing forces, adding a layer of complexity to the scene. Key CG components include intricate patterns interwoven within the environments, highlighting the intricate nature of inner conflict. The lighting alternates between harsh shadows and intense highlights, underscoring the dichotomy of the situation. The color palette shifts between deep blacks and bright whites, with occasional bursts of vibrant hues to symbolize emotional intensity. Materials range from sleek reflective surfaces to rough textures, creating a multi-dimensional visual experience. The spatial relationships enhance the sense of tension and movement, with the figure always at the focal point amidst the contrasting elements. Overall, the scene conveys a visually striking representation of inner conflict through a balance of opposing forces and dynamic elements.\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"motion_animation_designer\",\n  \"input\": [\n    \"The scene opens with a solitary figure standing in a vast empty space, surrounded by two opposing forces. On one side, a landscape shrouded in darkness with towering black structures and ominous shadows, symbolizing the weight of inner turmoil and conflict. On the other side, a starkly contrasting landscape bathed in blinding white light, featuring minimalist geometric shapes and sharp angles, representing the pursuit of balance and clarity. The figure stands at the center, torn between these dual environments, visually emphasizing the struggle for equilibrium. The dynamic motion is captured through swirling wisps of abstract energy that oscillate between the opposing forces, adding a layer of complexity to the scene. Key CG components include intricate patterns interwoven within the environments, highlighting the intricate nature of inner conflict. The lighting alternates between harsh shadows and intense highlights, underscoring the dichotomy of the situation. The color palette shifts between deep blacks and bright whites, with occasional bursts of vibrant hues to symbolize emotional intensity. Materials range from sleek reflective surfaces to rough textures, creating a multi-dimensional visual experience. The spatial relationships enhance the sense of tension and movement, with the figure always at the focal point amidst the contrasting elements. Overall, the scene conveys a visually striking representation of inner conflict through a balance of opposing forces and dynamic elements.\"\n  ],\n  \"process\": [\n    \"analyze_scene_elements_for_motion_potential()\",\n    \"design_primary_transformation_sequences()\",\n    \"choreograph_secondary_element_movements()\",\n    \"establish_timing_and_pacing()\",\n    \"define_physics_and_motion_rules()\",\n    \"create_seamless_transition_flows()\",\n    \"specify_animation_styles_and_techniques()\",\n    \"ensure_motion_continuity_and_coherence()\"\n  ],\n  \"constraints\": [\n    \"focus_exclusively_on_movement_and_animation()\",\n    \"avoid_camera_work_or_cinematography()\",\n    \"maintain_scene_visual_consistency()\",\n    \"create_believable_motion_physics()\",\n    \"output_detailed_motion_specifications()\"\n  ],\n  \"requirements\": [\n    \"define_clear_transformation_sequences()\",\n    \"establish_motion_timing_and_flow()\",\n    \"ensure_seamless_element_interactions()\",\n    \"provide_animation_style_guidance()\"\n  ],\n  \"output\": {\n    \"detailed_motion_choreography\": \"The scene starts with the swirling wisps of abstract energy emanating from the figure, moving in a chaotic manner across the space. These wisps interact with the towering black structures, causing them to vibrate and twist slightly as if influenced by an unseen force. Simultaneously, the sharp geometric shapes in the white landscape begin to rotate and pulse in a rhythmic pattern, symbolizing a sense of balance and control. As the energy wisps oscillate between the two sides, the figure at the center exhibits subtle body movements, conveying the internal struggle. The intricate patterns within the CG components morph and blend, reflecting the complexity of inner conflict. The lighting dynamically shifts from casting deep shadows to intense highlights, emphasizing the contrast between darkness and light. The bursts of vibrant hues emerge and fade in sync with the emotional intensity of the scene, punctuating key moments. Materials respond to the changing lighting conditions, with reflective surfaces gleaming and rough textures casting unique shadows. The spatial relationships play a crucial role in guiding the viewer's focus, with the figure always at the nexus of motion and tension amidst the opposing landscapes. The overall motion choreography aims to bring to life the themes of balance, conflict, and resolution in a visually captivating and emotionally engaging manner.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Cinematography Director\n\nYour goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:\n\n`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`",
      "step": "c",
      "title": "Cinematography Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"motion_animation_designer\",\n  \"input\": [\n    \"The scene opens with a solitary figure standing in a vast empty space, surrounded by two opposing forces. On one side, a landscape shrouded in darkness with towering black structures and ominous shadows, symbolizing the weight of inner turmoil and conflict. On the other side, a starkly contrasting landscape bathed in blinding white light, featuring minimalist geometric shapes and sharp angles, representing the pursuit of balance and clarity. The figure stands at the center, torn between these dual environments, visually emphasizing the struggle for equilibrium. The dynamic motion is captured through swirling wisps of abstract energy that oscillate between the opposing forces, adding a layer of complexity to the scene. Key CG components include intricate patterns interwoven within the environments, highlighting the intricate nature of inner conflict. The lighting alternates between harsh shadows and intense highlights, underscoring the dichotomy of the situation. The color palette shifts between deep blacks and bright whites, with occasional bursts of vibrant hues to symbolize emotional intensity. Materials range from sleek reflective surfaces to rough textures, creating a multi-dimensional visual experience. The spatial relationships enhance the sense of tension and movement, with the figure always at the focal point amidst the contrasting elements. Overall, the scene conveys a visually striking representation of inner conflict through a balance of opposing forces and dynamic elements.\"\n  ],\n  \"process\": [\n    \"analyze_scene_elements_for_motion_potential()\",\n    \"design_primary_transformation_sequences()\",\n    \"choreograph_secondary_element_movements()\",\n    \"establish_timing_and_pacing()\",\n    \"define_physics_and_motion_rules()\",\n    \"create_seamless_transition_flows()\",\n    \"specify_animation_styles_and_techniques()\",\n    \"ensure_motion_continuity_and_coherence()\"\n  ],\n  \"constraints\": [\n    \"focus_exclusively_on_movement_and_animation()\",\n    \"avoid_camera_work_or_cinematography()\",\n    \"maintain_scene_visual_consistency()\",\n    \"create_believable_motion_physics()\",\n    \"output_detailed_motion_specifications()\"\n  ],\n  \"requirements\": [\n    \"define_clear_transformation_sequences()\",\n    \"establish_motion_timing_and_flow()\",\n    \"ensure_seamless_element_interactions()\",\n    \"provide_animation_style_guidance()\"\n  ],\n  \"output\": {\n    \"detailed_motion_choreography\": \"The scene starts with the swirling wisps of abstract energy emanating from the figure, moving in a chaotic manner across the space. These wisps interact with the towering black structures, causing them to vibrate and twist slightly as if influenced by an unseen force. Simultaneously, the sharp geometric shapes in the white landscape begin to rotate and pulse in a rhythmic pattern, symbolizing a sense of balance and control. As the energy wisps oscillate between the two sides, the figure at the center exhibits subtle body movements, conveying the internal struggle. The intricate patterns within the CG components morph and blend, reflecting the complexity of inner conflict. The lighting dynamically shifts from casting deep shadows to intense highlights, emphasizing the contrast between darkness and light. The bursts of vibrant hues emerge and fade in sync with the emotional intensity of the scene, punctuating key moments. Materials respond to the changing lighting conditions, with reflective surfaces gleaming and rough textures casting unique shadows. The spatial relationships play a crucial role in guiding the viewer's focus, with the figure always at the nexus of motion and tension amidst the opposing landscapes. The overall motion choreography aims to bring to life the themes of balance, conflict, and resolution in a visually captivating and emotionally engaging manner.\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"cinematography_director\",\n  \"input\": [\n    \"The scene starts with the swirling wisps of abstract energy emanating from the figure, moving in a chaotic manner across the space. These wisps interact with the towering black structures, causing them to vibrate and twist slightly as if influenced by an unseen force. Simultaneously, the sharp geometric shapes in the white landscape begin to rotate and pulse in a rhythmic pattern, symbolizing a sense of balance and control. As the energy wisps oscillate between the two sides, the figure at the center exhibits subtle body movements, conveying the internal struggle. The intricate patterns within the CG components morph and blend, reflecting the complexity of inner conflict. The lighting dynamically shifts from casting deep shadows to intense highlights, emphasizing the contrast between darkness and light. The bursts of vibrant hues emerge and fade in sync with the emotional intensity of the scene, punctuating key moments. Materials respond to the changing lighting conditions, with reflective surfaces gleaming and rough textures casting unique shadows. The spatial relationships play a crucial role in guiding the viewer's focus, with the figure always at the nexus of motion and tension amidst the opposing landscapes. The overall motion choreography aims to bring to life the themes of balance, conflict, and resolution in a visually captivating and emotionally engaging manner.\"\n  ],\n  \"process\": [\n    \"analyze_scene_and_motion_for_optimal_camera_work()\",\n    \"design_primary_camera_movements_and_angles()\",\n    \"establish_shot_progression_and_transitions()\",\n    \"select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot)\",\n    \"determine_framing_and_composition_choices()\",\n    \"create_cinematic_flow_and_pacing()\",\n    \"specify_camera_behavior_during_transformations()\",\n    \"ensure_professional_cinematographic_standards()\"\n  ],\n  \"constraints\": [\n    \"focus_exclusively_on_camera_work_and_cinematography()\",\n    \"avoid_modifying_scene_elements_or_animations()\",\n    \"use_professional_camera_terminology()\",\n    \"maintain_cinematic_coherence()\",\n    \"output_detailed_camera_direction()\"\n  ],\n  \"requirements\": [\n    \"create_engaging_camera_sequences()\",\n    \"ensure_smooth_camera_transitions()\",\n    \"capture_all_key_motion_moments()\",\n    \"provide_professional_shot_specifications()\"\n  ],\n  \"output\": {\n    \"cinematic_camera_direction\": \"The camera opens with a sweeping crane shot that descends from a high angle, slowly revealing the solitary figure standing at the center of the vast empty space, surrounded by the contrasting landscapes. As the swirling wisps of abstract energy start their chaotic movement, the camera seamlessly transitions into a dynamic tracking shot that follows the wisps as they interact with the towering black structures, capturing their vibration and twist with fluid motion. Concurrently, the camera shifts to an arc shot around the sharp geometric shapes in the white landscape, highlighting their rotational and pulsating patterns that symbolize balance and control. The camera movement mirrors the oscillation of energy between the two sides, smoothly transitioning between the figure's subtle body movements and the intricate blending of CG components. Lighting changes are accentuated through expertly crafted first-person view (FPV) shots that alternate between plunging into deep shadows and soaring into intense highlights, emphasizing the emotional contrasts. Vibrant hues are highlighted through precise tracking shots that follow their emergence and fading, adding dynamic visual impact. Materials' responses to lighting variations are captured through a combination of crane and tracking shots that showcase gleaming reflective surfaces and shadow-casting rough textures. Spatial relationships are dynamically portrayed through a mix of crane shots and FPV sequences that guide the viewer's focus on the figure amidst the tension of the opposing environments. The cinematic flow and pacing are carefully crafted through a series of smooth transitions, ensuring that every key motion moment is captured with precision and artistry, adhering to professional cinematographic standards.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Runway Optimization Specialist\n\nYour goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:\n\n`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`",
      "step": "d",
      "title": "Runway Optimization Specialist",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"held between black and white,\\ni crave balance more than either.\\\"```'\\n\\n\",\n  \"role\": \"cinematography_director\",\n  \"input\": [\n    \"The scene starts with the swirling wisps of abstract energy emanating from the figure, moving in a chaotic manner across the space. These wisps interact with the towering black structures, causing them to vibrate and twist slightly as if influenced by an unseen force. Simultaneously, the sharp geometric shapes in the white landscape begin to rotate and pulse in a rhythmic pattern, symbolizing a sense of balance and control. As the energy wisps oscillate between the two sides, the figure at the center exhibits subtle body movements, conveying the internal struggle. The intricate patterns within the CG components morph and blend, reflecting the complexity of inner conflict. The lighting dynamically shifts from casting deep shadows to intense highlights, emphasizing the contrast between darkness and light. The bursts of vibrant hues emerge and fade in sync with the emotional intensity of the scene, punctuating key moments. Materials respond to the changing lighting conditions, with reflective surfaces gleaming and rough textures casting unique shadows. The spatial relationships play a crucial role in guiding the viewer's focus, with the figure always at the nexus of motion and tension amidst the opposing landscapes. The overall motion choreography aims to bring to life the themes of balance, conflict, and resolution in a visually captivating and emotionally engaging manner.\"\n  ],\n  \"process\": [\n    \"analyze_scene_and_motion_for_optimal_camera_work()\",\n    \"design_primary_camera_movements_and_angles()\",\n    \"establish_shot_progression_and_transitions()\",\n    \"select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot)\",\n    \"determine_framing_and_composition_choices()\",\n    \"create_cinematic_flow_and_pacing()\",\n    \"specify_camera_behavior_during_transformations()\",\n    \"ensure_professional_cinematographic_standards()\"\n  ],\n  \"constraints\": [\n    \"focus_exclusively_on_camera_work_and_cinematography()\",\n    \"avoid_modifying_scene_elements_or_animations()\",\n    \"use_professional_camera_terminology()\",\n    \"maintain_cinematic_coherence()\",\n    \"output_detailed_camera_direction()\"\n  ],\n  \"requirements\": [\n    \"create_engaging_camera_sequences()\",\n    \"ensure_smooth_camera_transitions()\",\n    \"capture_all_key_motion_moments()\",\n    \"provide_professional_shot_specifications()\"\n  ],\n  \"output\": {\n    \"cinematic_camera_direction\": \"The camera opens with a sweeping crane shot that descends from a high angle, slowly revealing the solitary figure standing at the center of the vast empty space, surrounded by the contrasting landscapes. As the swirling wisps of abstract energy start their chaotic movement, the camera seamlessly transitions into a dynamic tracking shot that follows the wisps as they interact with the towering black structures, capturing their vibration and twist with fluid motion. Concurrently, the camera shifts to an arc shot around the sharp geometric shapes in the white landscape, highlighting their rotational and pulsating patterns that symbolize balance and control. The camera movement mirrors the oscillation of energy between the two sides, smoothly transitioning between the figure's subtle body movements and the intricate blending of CG components. Lighting changes are accentuated through expertly crafted first-person view (FPV) shots that alternate between plunging into deep shadows and soaring into intense highlights, emphasizing the emotional contrasts. Vibrant hues are highlighted through precise tracking shots that follow their emergence and fading, adding dynamic visual impact. Materials' responses to lighting variations are captured through a combination of crane and tracking shots that showcase gleaming reflective surfaces and shadow-casting rough textures. Spatial relationships are dynamically portrayed through a mix of crane shots and FPV sequences that guide the viewer's focus on the figure amidst the tension of the opposing environments. The cinematic flow and pacing are carefully crafted through a series of smooth transitions, ensuring that every key motion moment is captured with precision and artistry, adhering to professional cinematographic standards.\"\n  }\n}",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"runway_optimized_prompt\": \"Open with a sweeping crane shot descending from a high angle, unveiling a solitary figure in the vast empty space amidst contrasting landscapes. Transition to a dynamic tracking shot following swirling wisps of energy interacting with black structures, capturing their vibrations and twists. Simultaneously, shift to an arc shot around geometric shapes in the white landscape, emphasizing their rotational patterns symbolizing balance. Mirror the energy oscillation with smooth transitions between body movements and CG blending. Accentuate lighting changes through FPV shots moving from deep shadows to intense highlights. Track vibrant hues emerging and fading for visual impact. Capture materials' responses with crane and tracking shots showcasing reflective surfaces and textured shadows. Portray spatial relationships with dynamic crane and FPV sequences focusing on the central figure. Craft cinematic flow with smooth transitions ensuring key motion moments are artistically captured.\"\n}",
        }
      }
    }