@ECHO OFF
TITLE Windows Customization Script

:: ----------------------------------------------------------------------------
:: Request Administrator Privileges
:: ----------------------------------------------------------------------------
NET SESSION >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    ECHO This script requires administrator privileges.
    ECHO Please right-click the script and select 'Run as administrator'.
    ECHO.
    PAUSE
    EXIT /B 1
)

ECHO Applying registry customizations...
ECHO.

:: ----------------------------------------------------------------------------
:: Add Applications to Run Dialog (App Paths)
:: ----------------------------------------------------------------------------
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /ve /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\Everything64.exe" /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\Everything.exe" /v Path /t REG_SZ /d "C:\Users\<USER>\Desktop\my\flow\home\__GOTO__\Apps\app_everything\exe\" /f
::

:: ----------------------------------------------------------------------------
:: Explorer Customizations
:: ----------------------------------------------------------------------------
ECHO Customizing File Explorer...
:: Show full path in the File Explorer title bar
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState" /v FullPath /t REG_DWORD /d 0 /f

:: Change Windows Explorer to automatically open 'This PC'
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" /v LaunchTo /t REG_DWORD /d 1 /f

:: Remove the "Shortcut" suffix when creating shortcuts
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "ShortcutNameTemplate" /t REG_SZ /d "%%s.lnk" /f

:: Set suffix added to filename when copying files
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\NamingTemplates" /v "CopyNameTemplate" /t REG_SZ /d "%%s 1" /f

:: Hide frequently and recently used files/folders in quick access
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowFrequent" /t REG_DWORD /d "0" /f
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v "ShowRecent" /t REG_DWORD /d "0" /f

:: Disable Automatic Folder Type Discovery
REG DELETE "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags" /f
REG ADD "HKCU\Software\Classes\Local Settings\Software\Microsoft\Windows\Shell\Bags\AllFolders\Shell" /v FolderType /t REG_SZ /d NotSpecified /f
ECHO Explorer customizations applied.
ECHO.

:: ----------------------------------------------------------------------------
:: System Behavior
:: ----------------------------------------------------------------------------
ECHO Adjusting system behavior...
:: Set Keyboard Delay and Keyboard Repeat Rate
reg add "HKCU\Control Panel\Keyboard" /v KeyboardDelay /t REG_SZ /d 0 /f
reg add "HKCU\Control Panel\Keyboard" /v KeyboardSpeed /t REG_SZ /d 310 /f

:: Disable "This App is Preventing Shutdown or Restart" Screen
REG ADD "HKCU\Control Panel\Desktop" /v "AutoEndTasks" /t REG_SZ /d "1" /f
REG ADD "HKCU\Control Panel\Desktop" /v "HungAppTimeout" /t REG_SZ /d "1000" /f
REG ADD "HKCU\Control Panel\Desktop" /v "WaitToKillAppTimeout" /t REG_SZ /d "1000" /f
REG ADD "HKLM\System\CurrentControlSet\Control" /v "WaitToKillServiceTimeout" /t REG_SZ /d "1000" /f
ECHO System behavior adjustments applied.
ECHO.

:: ----------------------------------------------------------------------------
:: Control Panel
:: ----------------------------------------------------------------------------
ECHO Configuring Control Panel options...
:: Show all tasks on control panel (God Mode)
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "InfoTip" /t REG_SZ /d "View list of all Control Panel tasks" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /v "System.ControlPanel.Category" /t REG_SZ /d "5" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\DefaultIcon" /ve /t REG_SZ /d "C:\Windows\System32\imageres.dll,-27" /f
REG ADD "HKLM\SOFTWARE\Classes\CLSID\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}\Shell\Open\Command" /ve /t REG_SZ /d "explorer.exe shell:::{ED7BA470-8E54-465E-825C-99712043E01C}" /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel\NameSpace\{D15ED2E1-C75B-443c-BD7C-FC03B2F08C17}" /ve /t REG_SZ /d "All Tasks" /f
ECHO Control Panel options configured.
ECHO.

:: ----------------------------------------------------------------------------
:: Theme and Appearance
:: ----------------------------------------------------------------------------
ECHO Setting theme and appearance...
:: Enable Dark Theme
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f >nul
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f >nul
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v EnableTransparency /t REG_DWORD /d 1 /f >nul
ECHO Theme and appearance set.
ECHO.

:: ----------------------------------------------------------------------------
:: Taskbar Customizations
:: ----------------------------------------------------------------------------
ECHO Customizing Taskbar...
:: Taskbar: Disable Meet Now
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
REG ADD "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" /v HideSCAMeetNow /t REG_DWORD /d 1 /f
:: Taskbar: Disable People
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
REG ADD "HKCU\Software\Policies\Microsoft\Windows\Explorer" /v HidePeopleBar /t REG_DWORD /d 1 /f
:: Taskbar: Hide People icon (even if policy is not set)
REG ADD "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced\People" /v PeopleBand /t REG_DWORD /d 0 /f
:: Taskbar: Disable Weather, News and Interests on taskbar
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds" /v EnableFeeds /t REG_DWORD /d 0 /f
:: Taskbar: Hide Weather, News and Interests on taskbar (set view mode to off)
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Feeds" /v ShellFeedsTaskbarViewMode /t REG_DWORD /d 2 /f
:: Changes the Windows 11 taskbar alignment to the left
REG ADD HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced /v TaskbarAl /t REG_DWORD /d 0 /f
ECHO Taskbar customizations applied.
ECHO.

:: ----------------------------------------------------------------------------
:: Disable Features
:: ----------------------------------------------------------------------------
ECHO Disabling selected features...
:: Disable Cortana
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCloudSearch /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortana /t REG_DWORD /d 0 /f
REG ADD "HKLM\SOFTWARE\Policies\Microsoft\Windows\Windows Search" /v AllowCortanaAboveLock /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaEnabled /t REG_DWORD /d 0 /f
REG ADD "HKCU\Software\Microsoft\Windows\CurrentVersion\Search" /v CortanaConsent /t REG_DWORD /d 0 /f
ECHO Selected features disabled.
ECHO.

:: ----------------------------------------------------------------------------
:: Photo Viewer
:: ----------------------------------------------------------------------------
ECHO Setting up Legacy Photo Viewer...
:: Enable legacy photo viewer file associations
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKLM\SOFTWARE\Microsoft\Windows Photo Viewer\Capabilities\FileAssociations" /v ".%%~a" /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f
)
:: Set legacy photo viewer as default for current user (this part may require user to choose manually in Default Apps for some extensions)
for %%a in (tif tiff bmp dib gif jfif jpe jpeg jpg jxr png) do (
    REG ADD "HKCU\SOFTWARE\Classes\.%%~a" /ve /t REG_SZ /d "PhotoViewer.FileAssoc.Tiff" /f
)
ECHO Legacy Photo Viewer setup attempted. You might need to manually set defaults for some image types.
ECHO.

:: ----------------------------------------------------------------------------
:: Apply System Parameter Changes and Restart Explorer
:: ----------------------------------------------------------------------------
ECHO Applying system parameter changes and restarting Explorer...
RUNDLL32.EXE USER32.DLL,UpdatePerUserSystemParameters
taskkill /f /im explorer.exe
start explorer.exe
ECHO Explorer restarted.
ECHO.

:: ----------------------------------------------------------------------------
:: Exit with message (auto-close after 15 seconds)
:: ----------------------------------------------------------------------------
ECHO.
ECHO All customizations have been applied.
ECHO Window will close automatically in 15 seconds ...
PING 127.0.0.1 -n 16 > NUL
EXIT
:: ----------------------------------------------------------------------------